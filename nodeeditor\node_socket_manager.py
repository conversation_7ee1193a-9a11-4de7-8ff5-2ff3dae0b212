# -*- coding: utf-8 -*-
"""
Node Socket Manager - Dinamik socket yönetimi için sistem
"""
from typing import List, Dict, Optional, TYPE_CHECKING, Union
from enum import Enum
from qtpy.QtCore import QObject, Signal

from nodeeditor.node_socket import Socket, LEFT_CENTER, RIGHT_CENTER, LEFT_TOP, LEFT_BOTTOM, RIGHT_TOP, RIGHT_BOTTOM
from apps.execution_node_editor.core.data_types import DataType

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class SocketPosition(Enum):
    """Socket pozisyon türleri"""
    LEFT_TOP = LEFT_TOP
    LEFT_CENTER = LEFT_CENTER
    LEFT_BOTTOM = LEFT_BOTTOM
    RIGHT_TOP = RIGHT_TOP
    RIGHT_CENTER = RIGHT_CENTER
    RIGHT_BOTTOM = RIGHT_BOTTOM


class SocketDefinition:
    """Socket tanımı"""
    
    def __init__(self,
                 name: str,
                 socket_type: Union[DataType, str] = DataType.STRING,
                 position: SocketPosition = SocketPosition.LEFT_CENTER,
                 is_input: bool = True,
                 multi_edges: bool = True,
                 label: str = None,
                 description: str = "",
                 required: bool = False,
                 default_value = None):
        """
        Socket tanımı
        
        :param name: Socket adı
        :param socket_type: Socket veri türü
        :param position: Socket pozisyonu
        :param is_input: Giriş socket'i mi
        :param multi_edges: Çoklu bağlantı destekler mi
        :param label: Görüntülenecek etiket
        :param description: Açıklama
        :param required: Zorunlu mu
        :param default_value: Varsayılan değer
        """
        self.name = name
        self.socket_type = socket_type if isinstance(socket_type, DataType) else DataType(socket_type)
        self.position = position
        self.is_input = is_input
        self.multi_edges = multi_edges
        self.label = label or name.replace('_', ' ').title()
        self.description = description
        self.required = required
        self.default_value = default_value


class SocketRegistry:
    """Socket kayıt sistemi"""
    
    def __init__(self):
        self._socket_definitions: Dict[str, SocketDefinition] = {}
        self._socket_instances: Dict[str, Socket] = {}
        self._position_counters: Dict[SocketPosition, int] = {pos: 0 for pos in SocketPosition}
    
    def register_socket(self, definition: SocketDefinition) -> str:
        """Socket tanımını kaydet"""
        socket_id = f"{definition.name}_{id(definition)}"
        self._socket_definitions[socket_id] = definition
        return socket_id
    
    def unregister_socket(self, socket_id: str):
        """Socket tanımını kaldır"""
        if socket_id in self._socket_definitions:
            del self._socket_definitions[socket_id]
        if socket_id in self._socket_instances:
            del self._socket_instances[socket_id]
    
    def get_definition(self, socket_id: str) -> Optional[SocketDefinition]:
        """Socket tanımını al"""
        return self._socket_definitions.get(socket_id)
    
    def get_instance(self, socket_id: str) -> Optional[Socket]:
        """Socket instance'ını al"""
        return self._socket_instances.get(socket_id)
    
    def set_instance(self, socket_id: str, socket: Socket):
        """Socket instance'ını kaydet"""
        self._socket_instances[socket_id] = socket
    
    def get_sockets_by_position(self, position: SocketPosition) -> List[str]:
        """Belirli pozisyondaki socket'ları al"""
        return [sid for sid, defn in self._socket_definitions.items() 
                if defn.position == position]
    
    def get_input_sockets(self) -> List[str]:
        """Giriş socket'larını al"""
        return [sid for sid, defn in self._socket_definitions.items() if defn.is_input]
    
    def get_output_sockets(self) -> List[str]:
        """Çıkış socket'larını al"""
        return [sid for sid, defn in self._socket_definitions.items() if not defn.is_input]
    
    def clear(self):
        """Tüm socket'ları temizle"""
        self._socket_definitions.clear()
        self._socket_instances.clear()
        self._position_counters = {pos: 0 for pos in SocketPosition}


class SocketFactory:
    """Socket oluşturucu"""
    
    @staticmethod
    def create_socket(node: 'Node', definition: SocketDefinition, index: int = 0) -> Socket:
        """Socket oluştur"""
        socket = Socket(
            node=node,
            index=index,
            position=definition.position.value,
            socket_name=definition.name,
            socket_type=definition.socket_type,
            multi_edges=definition.multi_edges,
            count_on_this_node_side=1,  # Bu daha sonra güncellenir
            is_input=definition.is_input,
            label=definition.label
        )
        
        # Ek özellikler
        socket.description = definition.description
        socket.required = definition.required
        socket.default_value = definition.default_value
        
        return socket
    
    @staticmethod
    def create_input_socket(node: 'Node', name: str, socket_type: DataType = DataType.STRING,
                          label: str = None, required: bool = False) -> SocketDefinition:
        """Giriş socket tanımı oluştur"""
        return SocketDefinition(
            name=name,
            socket_type=socket_type,
            position=SocketPosition.LEFT_CENTER,
            is_input=True,
            label=label,
            required=required
        )
    
    @staticmethod
    def create_output_socket(node: 'Node', name: str, socket_type: DataType = DataType.STRING,
                           label: str = None) -> SocketDefinition:
        """Çıkış socket tanımı oluştur"""
        return SocketDefinition(
            name=name,
            socket_type=socket_type,
            position=SocketPosition.RIGHT_CENTER,
            is_input=False,
            label=label
        )


class NodeSocketManager(QObject):
    """Node socket yöneticisi"""
    
    # Signals
    socketAdded = Signal(str, object)  # socket_id, socket
    socketRemoved = Signal(str)  # socket_id
    socketPositionChanged = Signal(str, object)  # socket_id, new_position
    socketsReorganized = Signal()  # Socket'lar yeniden düzenlendiğinde
    
    def __init__(self, node: 'Node'):
        super().__init__()
        self.node = node
        self.registry = SocketRegistry()
        self.auto_position = True
        self.auto_index = True
        
        # Position preferences
        self.input_positions = [SocketPosition.LEFT_CENTER, SocketPosition.LEFT_TOP, SocketPosition.LEFT_BOTTOM]
        self.output_positions = [SocketPosition.RIGHT_CENTER, SocketPosition.RIGHT_TOP, SocketPosition.RIGHT_BOTTOM]
    
    def add_socket(self, definition: SocketDefinition) -> str:
        """Socket ekle"""
        # Socket'i kaydet
        socket_id = self.registry.register_socket(definition)
        
        # Pozisyon ve index'i otomatik ayarla
        if self.auto_position:
            definition.position = self._get_next_position(definition.is_input)
        
        index = self._get_next_index(definition.position) if self.auto_index else 0
        
        # Socket oluştur
        socket = SocketFactory.create_socket(self.node, definition, index)
        self.registry.set_instance(socket_id, socket)
        
        # Node'un socket listesine ekle
        if definition.is_input:
            self.node.inputs.append(socket)
        else:
            self.node.outputs.append(socket)
        
        # Socket sayısını güncelle
        self._update_socket_counts()
        
        # Scene'e ekle
        if hasattr(self.node, 'scene') and self.node.scene:
            self.node.scene.grScene.addItem(socket.grSocket)
        
        # Signal emit et
        self.socketAdded.emit(socket_id, socket)
        
        return socket_id
    
    def remove_socket(self, socket_id: str) -> bool:
        """Socket kaldır"""
        socket = self.registry.get_instance(socket_id)
        if not socket:
            return False
        
        # Bağlı edge'leri kaldır
        for edge in socket.edges.copy():
            edge.remove()
        
        # Node'un socket listesinden kaldır
        if socket in self.node.inputs:
            self.node.inputs.remove(socket)
        elif socket in self.node.outputs:
            self.node.outputs.remove(socket)
        
        # Scene'den kaldır
        if hasattr(self.node, 'scene') and self.node.scene:
            self.node.scene.grScene.removeItem(socket.grSocket)
        
        # Registry'den kaldır
        self.registry.unregister_socket(socket_id)
        
        # Socket sayısını güncelle
        self._update_socket_counts()
        
        # Index'leri yeniden düzenle
        self._reorganize_socket_indices()
        
        # Signal emit et
        self.socketRemoved.emit(socket_id)
        
        return True
    
    def move_socket(self, socket_id: str, new_position: SocketPosition) -> bool:
        """Socket pozisyonunu değiştir"""
        definition = self.registry.get_definition(socket_id)
        socket = self.registry.get_instance(socket_id)
        
        if not definition or not socket:
            return False
        
        # Pozisyonu güncelle
        old_position = definition.position
        definition.position = new_position
        socket.position = new_position.value
        
        # Socket pozisyonunu yeniden hesapla
        socket.setSocketPosition()
        
        # Index'leri yeniden düzenle
        self._reorganize_socket_indices()
        
        # Signal emit et
        self.socketPositionChanged.emit(socket_id, new_position)
        
        return True
    
    def get_socket_by_name(self, name: str) -> Optional[Socket]:
        """İsme göre socket al"""
        for socket_id, definition in self.registry._socket_definitions.items():
            if definition.name == name:
                return self.registry.get_instance(socket_id)
        return None
    
    def get_sockets_by_type(self, socket_type: DataType, is_input: bool = None) -> List[Socket]:
        """Türe göre socket'ları al"""
        sockets = []
        for socket_id, definition in self.registry._socket_definitions.items():
            if definition.socket_type == socket_type:
                if is_input is None or definition.is_input == is_input:
                    socket = self.registry.get_instance(socket_id)
                    if socket:
                        sockets.append(socket)
        return sockets
    
    def clear_sockets(self):
        """Tüm socket'ları temizle"""
        # Mevcut socket'ları kaldır
        for socket in self.node.inputs.copy():
            for edge in socket.edges.copy():
                edge.remove()
            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.removeItem(socket.grSocket)
        
        for socket in self.node.outputs.copy():
            for edge in socket.edges.copy():
                edge.remove()
            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.removeItem(socket.grSocket)
        
        self.node.inputs.clear()
        self.node.outputs.clear()
        self.registry.clear()
    
    def _get_next_position(self, is_input: bool) -> SocketPosition:
        """Sonraki uygun pozisyonu al"""
        positions = self.input_positions if is_input else self.output_positions
        
        # En az socket'a sahip pozisyonu bul
        min_count = float('inf')
        best_position = positions[0]
        
        for position in positions:
            count = len(self.registry.get_sockets_by_position(position))
            if count < min_count:
                min_count = count
                best_position = position
        
        return best_position
    
    def _get_next_index(self, position: SocketPosition) -> int:
        """Belirli pozisyon için sonraki index'i al"""
        sockets_at_position = self.registry.get_sockets_by_position(position)
        return len(sockets_at_position)
    
    def _update_socket_counts(self):
        """Socket sayılarını güncelle"""
        # Her pozisyondaki socket sayısını güncelle
        for position in SocketPosition:
            sockets = self.registry.get_sockets_by_position(position)
            for socket_id in sockets:
                socket = self.registry.get_instance(socket_id)
                if socket:
                    socket.count_on_this_node_side = len(sockets)
    
    def _reorganize_socket_indices(self):
        """Socket index'lerini yeniden düzenle"""
        # Her pozisyon için socket'ları sırala ve index'leri güncelle
        for position in SocketPosition:
            socket_ids = self.registry.get_sockets_by_position(position)
            for i, socket_id in enumerate(socket_ids):
                socket = self.registry.get_instance(socket_id)
                if socket:
                    socket.index = i
                    socket.setSocketPosition()
        
        # Connected edge'leri güncelle
        if hasattr(self.node, 'updateConnectedEdges'):
            self.node.updateConnectedEdges()
        
        # Signal emit et
        self.socketsReorganized.emit()
    
    def set_auto_position(self, enabled: bool):
        """Otomatik pozisyonlandırmayı aç/kapat"""
        self.auto_position = enabled
    
    def set_auto_index(self, enabled: bool):
        """Otomatik index'lemeyi aç/kapat"""
        self.auto_index = enabled
    
    def set_position_preferences(self, input_positions: List[SocketPosition], 
                                output_positions: List[SocketPosition]):
        """Pozisyon tercihlerini ayarla"""
        self.input_positions = input_positions
        self.output_positions = output_positions
