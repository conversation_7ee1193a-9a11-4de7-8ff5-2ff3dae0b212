#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Terminal çıktısını log.md dosyasına yazan betik
"""

import subprocess
import sys
import time
import os
from datetime import datetime

def run_and_log(command, log_file="log.md"):
    """
    Komutu çalıştırır ve çıktısını log dosyasına yazar
    
    Args:
        command (str): Çalıştırılacak komut
        log_file (str): Log dosyası adı
    """
    
    # Log dosyasını temizle (önceki içeriği sil)
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(f"# Terminal Log\n\n")
        f.write(f"**Komut:** `{command}`\n\n")
        f.write(f"**Başlangıç Zamanı:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## Çıktı\n\n```\n")
    
    try:
        # Komutu çalıştır
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"Komut çalıştırılıyor: {command}")
        print(f"Log dosyası: {log_file}")
        print("Çıktı log dosyasına yazılıyor...")
        print("Durdurmak için Ctrl+C basın\n")
        
        # Çıktıyı gerçek zamanlı olarak oku ve yaz
        with open(log_file, 'a', encoding='utf-8') as f:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # Hem terminale hem de dosyaya yaz
                    print(output.strip())
                    f.write(output)
                    f.flush()  # Hemen dosyaya yaz
        
        # Process'in bitmesini bekle
        return_code = process.poll()
        
        # Log dosyasını kapat
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("```\n\n")
            f.write(f"**Bitiş Zamanı:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Return Code:** {return_code}\n")
        
        print(f"\nKomut tamamlandı. Return code: {return_code}")
        
    except KeyboardInterrupt:
        print("\n\nKullanıcı tarafından durduruldu (Ctrl+C)")
        process.terminate()
        
        # Log dosyasını kapat
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("```\n\n")
            f.write(f"**Durdurulma Zamanı:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("**Durum:** Kullanıcı tarafından durduruldu (Ctrl+C)\n")
        
    except Exception as e:
        print(f"\nHata oluştu: {e}")
        
        # Log dosyasını kapat
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("```\n\n")
            f.write(f"**Hata Zamanı:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Hata:** {e}\n")

def main():
    """Ana fonksiyon"""
    if len(sys.argv) < 2:
        print("Kullanım: python log_terminal.py <komut>")
        print("Örnek: python log_terminal.py \"python -m apps.execution_node_editor.main\"")
        sys.exit(1)
    
    # Komut argümanlarını birleştir
    command = " ".join(sys.argv[1:])
    
    # Log dosyası adı
    log_file = "log.md"
    
    # Komutu çalıştır ve logla
    run_and_log(command, log_file)

if __name__ == "__main__":
    main()
