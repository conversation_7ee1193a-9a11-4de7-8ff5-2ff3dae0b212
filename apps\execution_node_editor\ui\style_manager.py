"""
Stil yönetimi için ayrı modül
Mevcut window.py'deki stil kodlarını organize eder
"""
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFontDatabase


class StyleManager:
    """Uygulama stillerini yöneten sınıf"""
    
    # Ana tema stilleri
    DARK_THEME_STYLE = """
QWidget {
    background-color: #2d2d2d;
    color: #e0e0e0;
}
QDockWidget {
    background-color: #383838;
    border: 1px solid #414141;
    border-radius: 6px;
}
QDockWidget::title {
    background: #232323;
    color: #e0e0e0;
    font-size: 13px;
    font-weight: bold;
    padding: 8px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
QMenuBar, QMenu {
    background: #232323;
    color: #e0e0e0;
}
QStatusBar {
    background: #232323;
    color: #cccccc;
    border-top: 1px solid #414141;
}
QPushButton {
    background-color: #414141;
    color: #e0e0e0;
    border: 1px solid #5a5a5a;
    border-radius: 5px;
    padding: 4px 12px;
}
QPushButton:hover {
    background-color: #5a5a5a;
    color: #fff;
}
QTreeView, QListView, QAbstractItemView {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: none;
}
QMainWindow::separator {
    background: #414141;
    width: 1px;
    height: 1px;
}
"""

    # Ek global stiller
    GLOBAL_STYLE_ADDITIONS = """
QDockWidget, QDockWidget QWidget { background-color: #232323; }
QTreeView, QListView, QAbstractItemView { background-color: #232323; color: #cccccc; border: none; }
QWidget { background-color: #232323; color: #cccccc; }
"""

    # MDI alt pencere stilleri
    MDI_SUBWINDOW_STYLE = 'background: #232323; border: none;'

    def __init__(self):
        self.current_theme = "dark"
        
    @staticmethod
    def load_fonts():
        """Uygulama fontlarını yükler"""
        QFontDatabase.addApplicationFont("apps/execution_node_editor/assets/fonts/Roboto-Regular.ttf")
        QFontDatabase.addApplicationFont("apps/execution_node_editor/assets/fonts/Roboto-Bold.ttf")
    
    def apply_main_window_style(self, main_window):
        """Ana pencereye stil uygular"""
        main_window.setStyleSheet(self.DARK_THEME_STYLE)
    
    def apply_global_styles(self):
        """Uygulama genelinde stiller uygular"""
        app = QApplication.instance()
        if app:
            current_style = app.styleSheet()
            app.setStyleSheet(current_style + "\n" + self.GLOBAL_STYLE_ADDITIONS)
    
    def get_mdi_subwindow_style(self):
        """MDI alt pencere stilini döndürür"""
        return self.MDI_SUBWINDOW_STYLE
    
    def set_theme(self, theme_name):
        """Tema değiştirir (gelecekte farklı temalar için)"""
        self.current_theme = theme_name
        # Gelecekte farklı temalar eklenebilir
    
    def get_current_theme(self):
        """Mevcut temayı döndürür"""
        return self.current_theme


# Global stil yöneticisi instance
style_manager = StyleManager()
