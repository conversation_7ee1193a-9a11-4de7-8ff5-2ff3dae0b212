from apps.execution_node_editor.conf import nodeTypes, create_node, LISTBOX_MIMETYPE
from nodeeditor.node_socket import Socket, SocketDefinition
from qtpy.QtGui import QIcon, QPixmap
from qtpy.QtCore import QDataStream, QIODevice, Qt, QEvent
from qtpy.QtWidgets import QAction, QGraphicsProxyWidget, QMenu, QLineEdit, QWidgetAction, QHBoxLayout, QLabel, QWidget, QStyle, QProxyStyle

from nodeeditor.node_edge import EDGE_TYPE_DIRECT, EDGE_TYPE_BEZIER, EDGE_TYPE_SQUARE
from nodeeditor.node_graphics_view import MODE_EDGE_DRAG
from nodeeditor.utils import dumpException
from apps.execution_node_editor.localization import i18n
from nodeeditor.node_add_menu import init_nodes_context_menu
from nodeeditor.node_editor_widget import NodeEditorWidget

DEBUG = False
DEBUG_CONTEXT = False

# Alt menülerin ana menüye çok yakın ama üst üste binmeden açılması için özel bir stil
class ZeroOffsetMenuStyle(QProxyStyle):
    def subElementRect(self, element, option, widget=None):
        rect = super().subElementRect(element, option, widget)
        # Alt menü açılırken offset'i küçük bir değere ayarla (ör: -4 px)
        if element == QStyle.SE_MenuScroller:
            return rect
        if element == QStyle.SE_MenuSubMenuPopup:
            rect.moveLeft(rect.left() - 4)  # -12 yerine -4 px
        return rect

class SubWindow(NodeEditorWidget):
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setTitle()
        self.scene.addHasBeenModifiedListener(self.setTitle)
        self.scene.history.addHistoryRestoredListener(self.onHistoryRestored)
        self.scene.addDragEnterListener(self.onDragEnter)
        self.scene.addDropListener(self.onDrop)
        self.view.addDropListener(self.onDrop)
        self._close_event_listeners = []
        # Install event filter on the graphics view
        self.view.installEventFilter(self)

    def onHistoryRestored(self):
        self.doEvalOutputs()

    def fileLoad(self, filename):
        if super().fileLoad(filename):
            self.doEvalOutputs()
            return True

        return False

    def initNodesContextMenu(self):
        context_menu = QMenu(self)
        # Alt menülerin ana menüye çok yakın ama üst üste binmeden açılması için özel stil uygula
        context_menu.setStyle(ZeroOffsetMenuStyle())
        # Modern ve Unity tarzı context menu QSS + alt menü aralığı
        context_menu.setStyleSheet("""
QMenu {
    background-color: #232323;
    color: #e0e0e0;
    border: 1.5px solid #414141;
    border-radius: 8px;
    padding: 12px 8px 12px 8px;
    font-size: 13px;
    font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
}
QMenu::item {
    background: transparent;
    color: #e0e0e0;
    padding: 8px 32px 8px 32px;
    border-radius: 6px;
    margin: 4px 0;
    font-size: 13px;
    font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
    transition: background 0.2s, color 0.2s;
}
QMenu::item:selected {
    background: #414141;
    color: #ffcc00;
}
QMenu::separator {
    height: 1px;
    background: #414141;
    margin: 8px 0;
}
QMenu::icon {
    padding-left: 8px;
}
QMenu QMenu {
    border-radius: 8px;
    box-shadow: none;
    margin-left: 0px;
    margin-top: 0px;
    border: 1.5px solid #414141;
    background-color: #232323;
}
""")
        # Arama çubuğu ve ikon için widget oluştur
        search_widget = QWidget(context_menu)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(6)
        icon_label = QLabel()
        icon_path = "apps/execution_node_editor/assets/icons/Editor/search.png"
        icon_pixmap = QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        icon_label.setPixmap(icon_pixmap)
        icon_label.setStyleSheet("padding-left: 4px; padding-right: 2px;")
        search_box = QLineEdit()
        search_box.setPlaceholderText(i18n.get("search_node_placeholder"))
        search_box.setStyleSheet("background: #313131; color: #e0e0e0; border-radius: 5px; padding: 6px 10px; font-size: 13px;")
        search_layout.addWidget(icon_label)
        search_layout.addWidget(search_box)
        search_widget.setLayout(search_layout)
        search_action = QWidgetAction(context_menu)
        search_action.setDefaultWidget(search_widget)
        context_menu.addAction(search_action)
        # Node tiplerini ekleyecek fonksiyon
        def populate_menu(filter_text=""):
            # Önce eski menüleri temizle (arama çubuğu hariç)
            for act in context_menu.actions()[1:]:
                context_menu.removeAction(act)
            keys = list(nodeTypes.keys())
            keys.sort()
            for key in keys:
                # System kategorisini gizle
                if key == "system":
                    continue

                sub_menu = QMenu(i18n.get(key), context_menu)
                sub_menu.setStyleSheet(context_menu.styleSheet())
                types = nodeTypes[key]
                added = False
                for type in types:
                    if filter_text.lower() in type.lower():
                        action = sub_menu.addAction(i18n.get(type))
                        action.setData(type)
                        added = True
                if added:
                    context_menu.addMenu(sub_menu)
        # Arama çubuğu ile filtreleme
        search_box.textChanged.connect(lambda text: populate_menu(text))
        # İlk açılışta tüm node'ları göster
        populate_menu()
        return context_menu

    def setTitle(self):
        self.setWindowTitle(self.getUserFriendlyFilename())

    def addCloseEventListener(self, callback):
        self._close_event_listeners.append(callback)

    def closeEvent(self, event):
        for callback in self._close_event_listeners: callback(self, event)

    def onDragEnter(self, event):
        if event.mimeData().hasFormat(LISTBOX_MIMETYPE):
            event.acceptProposedAction()
        else:
            event.setAccepted(False)

    def onDrop(self, event):
        if event.mimeData().hasFormat(LISTBOX_MIMETYPE):
            eventData = event.mimeData().data(LISTBOX_MIMETYPE)
            dataStream = QDataStream(eventData, QIODevice.ReadOnly)
            pixmap = QPixmap()
            dataStream >> pixmap
            node_type = dataStream.readQString()  # Gerçek node_type'ı oku
            display_name = dataStream.readQString()  # Display name'i oku (kullanmayabiliriz)

            mouse_position = event.pos()
            scene_position = self.scene.grScene.views()[0].mapToScene(mouse_position)

            try:
                node = create_node(self.scene, node_type)
                if node:
                    node.setPos(scene_position.x(), scene_position.y())
                    self.scene.history.storeHistory("Created node %s" % node.__class__.__name__)
            except Exception as e:
                dumpException(e)

            event.setDropAction(Qt.MoveAction)
            event.accept()
        else:
            event.ignore()

    def contextMenuEvent(self, event):
        # Debug kodu kaldırıldı
        try:
            item = self.scene.getItemAt(event.pos())

            if type(item) == QGraphicsProxyWidget:
                item = item.widget()

            if hasattr(item, 'node') or hasattr(item, 'socket'):
                return  # super().contextMenuEvent(event) YOK!
            elif hasattr(item, 'edge'):
                self.handleEdgeContextMenu(event)
            else:
                self.handleNewNodeContextMenu(event)

            event.accept()
        except Exception as e:
            dumpException(e)

    def handleNodeContextMenu(self, event):
        pass

    def handleEdgeContextMenu(self, event):
        # Debug kodu kaldırıldı
        context_menu = QMenu(self)
        bezierAct = context_menu.addAction("Bezier Edge")
        directAct = context_menu.addAction("Direct Edge")
        squareAct = context_menu.addAction("Square Edge")
        action = context_menu.exec_(self.mapToGlobal(event.pos()))

        selected = None
        item = self.scene.getItemAt(event.pos())
        if hasattr(item, 'edge'):
            selected = item.edge

        if selected and action == bezierAct: selected.edge_type = EDGE_TYPE_BEZIER
        if selected and action == directAct: selected.edge_type = EDGE_TYPE_DIRECT
        if selected and action == squareAct: selected.edge_type = EDGE_TYPE_SQUARE

    # helper functions
    def determine_target_socket_of_node(self, was_dragged_flag, new_calc_node):
        target_socket = None
        if was_dragged_flag:
            if len(new_calc_node.inputs) > 0: target_socket = new_calc_node.inputs[0]
        else:
            if len(new_calc_node.outputs) > 0: target_socket = new_calc_node.outputs[0]
        return target_socket

    def finish_new_node_state(self, new_calc_node):
        self.scene.doDeselectItems()
        new_calc_node.grNode.doSelect(True)
        new_calc_node.grNode.onSelected()


    def handleNewNodeContextMenu(self, event):
        # Debug kodu kaldırıldı
        # Debug kodu kaldırıldı
        context_menu = init_nodes_context_menu(self)
        action = context_menu.exec_(self.mapToGlobal(event.pos()))

        if action is not None:
            node_type = action.data()
            node = create_node(self.scene, node_type)
            scene_pos = self.scene.getView().mapToScene(event.pos())
            if node:
                node.setPos(scene_pos.x(), scene_pos.y())
            if self.scene.getView().mode == MODE_EDGE_DRAG:
                target_socket = self.determine_target_socket_of_node(self.scene.getView().dragging.drag_start_socket.is_output, node)
                if target_socket is not None:
                    self.scene.getView().dragging.edgeDragEnd(target_socket.grSocket)
                    self.finish_new_node_state(node)
            else:
                self.scene.history.storeHistory("Created %s" % node.__class__.__name__)

    def eventFilter(self, watched, event):
        if watched == self.view and event.type() == QEvent.MouseButtonPress and event.button() == Qt.RightButton:
            return super().eventFilter(watched, event)
        return False

    def resizeEvent(self, event):
        super().resizeEvent(event)
