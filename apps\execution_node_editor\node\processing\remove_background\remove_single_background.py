import argparse
import cv2
import os
from rembg import remove, new_session

def remove_bg(input_path, output_path, model_name="u2net"):
    """Reads an image, removes the background using a specified rembg model, and saves the result."""
    try:
        # Check if input file exists
        if not os.path.exists(input_path):
            pass
            return

        # Load image with alpha channel if it exists, otherwise load as BGR
        img = cv2.imread(input_path, cv2.IMREAD_UNCHANGED)
        if img is None:
            pass
            return


        # Ensure input is BGR for rembg if it has alpha channel
        if len(img.shape) > 2 and img.shape[2] == 4:
            input_img_for_rembg = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        else:
            input_img_for_rembg = img

        # Create a session for the specified model
        session = new_session(model_name)

        # Use rembg with the specific session to remove the background
        # This returns a BGRA image (Blue, Green, Red, Alpha)
        img_no_bg = remove(input_img_for_rembg, session=session)

        # Check if rembg returned a valid image
        if img_no_bg is None or img_no_bg.shape[2] != 4:
             pass
             return

        # Save the processed image (with alpha channel)
        write_success = cv2.imwrite(output_path, img_no_bg)
        if not write_success:
             pass
        else:
             pass

    except ImportError as ie:
        pass
    except Exception as e:
        pass

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bir resmin arka planını belirli bir rembg modelini kullanarak kaldırır.")
    parser.add_argument("input_file", help="İşlenecek girdi resim dosyasının yolu (.png, .jpg, vb.)")
    parser.add_argument("output_file", help="Arka planı kaldırılmış çıktının kaydedileceği dosya yolu (.png önerilir)")
    parser.add_argument("-m", "--model", default="u2net", help="Kullanılacak rembg modeli (örn: u2net, u2netp, isnet-general-use, silueta)")
    args = parser.parse_args()

    remove_bg(args.input_file, args.output_file, args.model)
