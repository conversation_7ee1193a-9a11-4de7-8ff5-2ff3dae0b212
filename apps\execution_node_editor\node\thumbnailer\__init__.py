"""
Thumbnailer modülü - Düğümlerin üzerinde içerik önizlemesi göstermek için kullanılır.
"""
from .base_thumbnailer import BaseThumbnailer
from .image_thumbnailer import ImageThumbnailer
from .multi_image_thumbnailer import MultiImageThumbnailer
from .video_thumbnailer import VideoThumbnailer

# Thumbnailer factory - doğru thumbnailer tipini döndürür
def create_thumbnailer(content_type, scene=None, parent=None, parent_node=None):
    """
    İçerik tipine göre uygun thumbnailer nesnesini oluşturur ve döndürür.
    
    Args:
        content_type (str): İçerik tipi ('image', 'video', 'multi_image')
        scene: Sahne referansı
        parent: Ebeveyn öğe
        parent_node: Bağlı olduğu düğüm
        
    Returns:
        BaseThumbnailer: Uygun thumbnailer nesnesi
    """
    if content_type == 'image':
        return ImageThumbnailer(scene, parent, parent_node)
    elif content_type == 'video':
        return VideoThumbnailer(scene, parent, parent_node)
    elif content_type == 'multi_image':
        return MultiImageThumbnailer(scene, parent, parent_node)
    else:
        # Varsayılan olarak BaseThumbnailer döndür
        return BaseThumbnailer(scene, parent, parent_node)
