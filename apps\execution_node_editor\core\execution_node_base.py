from qtpy.QtGui import QImage, QColor, QBrush, QPen
from PyQt5.QtGui import <PERSON><PERSON><PERSON>terPath
from PyQt5.QtCore import Qt
from qtpy.QtCore import QRectF
from qtpy.QtWidgets import <PERSON><PERSON>abe<PERSON>

from nodeeditor.node_node import Node
from nodeeditor.node_content_widget import QDMNodeContentWidget
from nodeeditor.node_graphics_node import QDMGraphicsNode
from nodeeditor.node_socket import LEFT_CENTER, RIGHT_CENTER
from nodeeditor.utils import dumpException
from apps.execution_node_editor.localization import i18n
from apps.execution_node_editor.core.node_styles import NODE_STYLES
from apps.execution_node_editor.core.data_types import DataType

class GraphicsExecutionNode(QDMGraphicsNode):

    def initSizes(self, max_sockets):
        super().initSizes()
        style = getattr(self.node, 'style', {})
        if isinstance(style, str):
            style = {'theme': style}
        theme = style.get('theme', 'green_theme')
        theme_style = NODE_STYLES.get(theme, NODE_STYLES['green_theme'])

        # Başlangıç boyutları (content'e göre ayarlanacak)
        self.width = 220  # Sabit başlangıç genişliği
        self.height = 120  # Sabit başlangıç yüksekliği

        self.edge_roundness = 12
        self.edge_padding = 8
        self.title_horizontal_padding = 8
        self.title_vertical_padding = 10
        self.title_height = 36

        # Auto-resize özelliklerini ayarla
        self.min_width = 180
        self.min_height = 80
        self.max_width = 350
        self.max_height = 400
        self.auto_resize = True

        # Socket pozisyonlarını düzelt
        if hasattr(self.node, 'socket_spacing'):
            self.node.socket_spacing = 24  # Daha küçük spacing
        if hasattr(self.node, 'socket_offsets'):
            # Socket'ların node içinde kalması için offset'leri ayarla
            socket_radius = 6  # Socket'ın yarıçapı
            border_padding = 4  # Kenara olan mesafe
            self.node.socket_offsets = {
                1: socket_radius + border_padding,   # LEFT_TOP
                2: socket_radius + border_padding,   # LEFT_CENTER
                3: socket_radius + border_padding,   # LEFT_BOTTOM
                4: -(socket_radius + border_padding),  # RIGHT_TOP
                5: -(socket_radius + border_padding),  # RIGHT_CENTER
                6: -(socket_radius + border_padding),  # RIGHT_BOTTOM
            }

    def initAssets(self):
        super().initAssets()
        style = getattr(self.node, 'style', {})
        if isinstance(style, str):
            style = {'theme': style}
        theme = style.get('theme', 'green_theme')
        theme_style = NODE_STYLES.get(theme, NODE_STYLES['green_theme'])
        main_color = QColor(theme_style["header"])
        glow_color = QColor(theme_style["glow"])
        # Header ve glow için renkler
        self._brush_title = QBrush(main_color)
        self._brush_title_hover = QBrush(main_color.lighter(120))
        self._brush_title_selected = QBrush(main_color.darker(120))
        self._color_selected = glow_color
        self._pen_selected = QPen(glow_color.lighter(130))
        self._pen_selected.setWidthF(2.5)
        self._pen_default = QPen(QColor("#00000000"))
        self._pen_default.setWidthF(0)
        self._brush_background = QBrush(QColor("#181c22"))
        self._brush_hover = QBrush(QColor("#23282f"))
        self._color_shadow = main_color.darker(180)
        self._shadow.setColor(self._color_shadow)
        self._shadow.setBlurRadius(24)

    def paint(self, painter, QStyleOptionGraphicsItem, widget=None):
        # Modern header (üst kısım) - sadece üst köşeler yuvarlatılmış
        path_title = QPainterPath()
        path_title.setFillRule(Qt.WindingFill)
        path_title.moveTo(0, self.title_height)
        path_title.lineTo(0, self.edge_roundness)
        path_title.quadTo(0, 0, self.edge_roundness, 0)
        path_title.lineTo(self.width - self.edge_roundness, 0)
        path_title.quadTo(self.width, 0, self.width, self.edge_roundness)
        path_title.lineTo(self.width, self.title_height)
        path_title.closeSubpath()
        painter.setPen(Qt.NoPen)
        if self.isSelected():
            painter.setBrush(self._brush_title_selected)
            self._shadow.setColor(self._color_selected)
            self._shadow.setBlurRadius(32)
        elif self.hovered:
            painter.setBrush(self._brush_title_hover)
            self._shadow.setColor(self._color_selected.lighter(120))
            self._shadow.setBlurRadius(28)
        else:
            painter.setBrush(self._brush_title)
            self._shadow.setColor(self._color_shadow)
            self._shadow.setBlurRadius(24)
        painter.drawPath(path_title.simplified())

        # İçerik kısmı - sadece alt köşeler yuvarlatılmış
        path_content = QPainterPath()
        path_content.setFillRule(Qt.WindingFill)
        path_content.moveTo(0, self.title_height)
        path_content.lineTo(0, self.height - self.edge_roundness)
        path_content.quadTo(0, self.height, self.edge_roundness, self.height)
        path_content.lineTo(self.width - self.edge_roundness, self.height)
        path_content.quadTo(self.width, self.height, self.width, self.height - self.edge_roundness)
        path_content.lineTo(self.width, self.title_height)
        path_content.closeSubpath()
        painter.setPen(Qt.NoPen)
        if self.hovered:
            painter.setBrush(self._brush_hover)
        else:
            painter.setBrush(self._brush_background)
        painter.drawPath(path_content.simplified())

        # Outline
        path_outline = QPainterPath()
        painter.setBrush(Qt.NoBrush)
        if self.isSelected():
            painter.setPen(self._pen_selected)
            path_outline.addRoundedRect(0, 0, self.width, self.height, self.edge_roundness, self.edge_roundness)
        else:
            painter.setPen(self._pen_default)
            path_outline.addRoundedRect(-0.5, -0.5, self.width+1, self.height+1, self.edge_roundness, self.edge_roundness)
        painter.drawPath(path_outline.simplified())

        # Content widget'ı initialize et (sadece ilk kez)
        if not hasattr(self, '_content_initialized'):
            self.initContent()
            self._content_initialized = True

        # Status indicator (dirty/invalid)
        if self.node.isDirty() or self.node.isInvalid():
            # Status indicator çiz
            indicator_size = 8
            indicator_x = self.width - indicator_size - 4
            indicator_y = 4

            if self.node.isDirty():
                painter.setBrush(QBrush(QColor(255, 165, 0)))  # Orange
            elif self.node.isInvalid():
                painter.setBrush(QBrush(QColor(255, 0, 0)))    # Red

            painter.setPen(Qt.NoPen)
            painter.drawEllipse(indicator_x, indicator_y, indicator_size, indicator_size)

    def initContent(self):
        """Content widget'ı initialize et"""
        if self.content is not None:
            from qtpy.QtWidgets import QGraphicsProxyWidget, QWidget, QGraphicsWidget

            # Content widget türüne göre farklı işlem
            if isinstance(self.content, QGraphicsWidget):
                # QGraphicsWidget ise direkt kullan (proxy'ye gerek yok)
                self.content.setParentItem(self)
                self.grContent = self.content
            elif isinstance(self.content, QWidget):
                # QWidget ise proxy ile wrap et
                if not hasattr(self, 'grContent') or self.grContent is None:
                    self.grContent = QGraphicsProxyWidget(self)
                    self.grContent.setWidget(self.content)

            # İlk boyutlandırma
            self.updateContentGeometry()

    def updateContentGeometry(self):
        """Content geometrisini güncelle"""
        if self.content is None or not hasattr(self, 'grContent'):
            return

        from qtpy.QtWidgets import QGraphicsWidget
        from qtpy.QtCore import QRectF

        # Content area boyutunu hesapla
        content_width = max(10, self.width - 2 * self.edge_padding)
        content_height = max(10, self.height - self.title_height - 2 * self.edge_padding)

        # Pozisyon ayarla
        self.grContent.setPos(self.edge_padding, self.title_height + self.edge_padding)

        # Content widget türüne göre boyutlandırma
        if isinstance(self.content, QGraphicsWidget):
            # QGraphicsWidget için geometry ayarla
            self.content.setGeometry(QRectF(0, 0, content_width, content_height))
        else:
            # QWidget için boyut ayarla
            if hasattr(self.content, 'setFixedSize'):
                self.content.setFixedSize(int(content_width), int(content_height))
            elif hasattr(self.content, 'resize'):
                self.content.resize(int(content_width), int(content_height))

    def updateGeometry(self):
        """Node geometrisini content'e göre güncelle"""
        if not hasattr(self, 'auto_resize') or not self.auto_resize or not self.content:
            return

        # Content'in istediği boyutu al
        if hasattr(self.content, 'sizeHint'):
            content_size = self.content.sizeHint()

            # Yeni node boyutunu hesapla (daha konservatif)
            padding = 16  # Daha az padding
            new_width = content_size.width() + padding
            new_height = content_size.height() + self.title_height + padding

            # Min/max sınırları uygula
            min_width = getattr(self, 'min_width', 180)
            max_width = getattr(self, 'max_width', 350)
            min_height = getattr(self, 'min_height', 80)
            max_height = getattr(self, 'max_height', 400)

            new_width = max(min_width, min(new_width, max_width))
            new_height = max(min_height, min(new_height, max_height))

            # Boyut değişti mi kontrol et (daha hassas)
            if abs(new_width - self.width) > 5 or abs(new_height - self.height) > 5:
                # Geometry değişikliğini hazırla
                self.prepareGeometryChange()

                # Yeni boyutu ayarla
                self.width = new_width
                self.height = new_height

                # Content geometrisini güncelle
                self.updateContentGeometry()

                # Socket pozisyonlarını güncelle
                self.updateSocketPositions()

                # Scene'i güncelle
                self.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle"""
        if hasattr(self.node, 'inputs') and hasattr(self.node, 'outputs'):
            # Tüm socket'ların pozisyonlarını güncelle
            for socket in self.node.inputs + self.node.outputs:
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            # Bağlı edge'leri güncelle
            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()


class ExecutionContent(QDMNodeContentWidget):
    def initUI(self):
        from qtpy.QtWidgets import QVBoxLayout, QHBoxLayout
        from qtpy.QtCore import Qt

        # Ana layout oluştur
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(4, 4, 4, 4)
        self.layout.setSpacing(2)
        self.setLayout(self.layout)

        # Label oluştur
        label_key = getattr(self.node, 'content_label', None)
        if label_key:
            label_text = i18n.get(label_key)
        else:
            label_text = self.node.title if hasattr(self.node, 'title') else ''

        if label_text:  # Sadece text varsa label ekle
            self.lbl = QLabel(label_text, self)
            self.lbl.setObjectName(getattr(self.node, 'content_label_objname', 'execution_node_label'))
            self.lbl.setAlignment(Qt.AlignCenter)
            self.lbl.setWordWrap(True)
            self.layout.addWidget(self.lbl)
        else:
            # Boş label oluştur (uyumluluk için)
            self.lbl = QLabel("", self)
            self.lbl.setObjectName('execution_node_label')
            self.lbl.setAlignment(Qt.AlignCenter)
            self.layout.addWidget(self.lbl)

        # Uyumluluk için wdg_label alias'ı
        self.wdg_label = self.lbl

        # Minimum boyut ayarla
        self.setMinimumSize(120, 30)

        # Boyut ipucu ayarla
        self.setSizePolicy(self.sizePolicy().Expanding, self.sizePolicy().Minimum)

    def updateTexts(self):
        """Metinleri güncelle"""
        if hasattr(self, 'lbl'):
            label_key = getattr(self.node, 'content_label', None)
            if label_key:
                label_text = i18n.get(label_key)
                self.lbl.setText(label_text)

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        if hasattr(self, 'lbl') and self.lbl.text():
            # Label'ın boyutuna göre hesapla
            label_size = self.lbl.sizeHint()
            width = max(120, label_size.width() + 8)  # Padding ekle
            height = max(30, label_size.height() + 8)
            return QSize(width, height)
        else:
            return QSize(120, 30)  # Varsayılan boyut

    def updateNodeGeometry(self):
        """Node geometrisini güncelle - tüm content widget'lar için ortak"""
        if self.node and hasattr(self.node, 'grNode'):
            # Kısa bir gecikme ile güncelle (UI thread'i bloklamayı önle)
            from qtpy.QtCore import QTimer
            QTimer.singleShot(10, self._doUpdateGeometry)

    def _doUpdateGeometry(self):
        """Gerçek geometry güncellemesi - tüm content widget'lar için ortak"""
        if self.node and hasattr(self.node, 'grNode'):
            # Node'un graphics node'una boyut güncellemesi gönder
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
            elif hasattr(self.node.grNode, 'updateContentGeometry'):
                self.node.grNode.updateContentGeometry()

            # Socket pozisyonlarını güncelle
            self.updateSocketPositions()

            # Scene'i güncelle
            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle - tüm content widget'lar için ortak"""
        if self.node:
            # Tüm socket'ların pozisyonlarını güncelle
            for socket in getattr(self.node, 'inputs', []) + getattr(self.node, 'outputs', []):
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            # Bağlı edge'leri güncelle
            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()


class ExecutionNode(Node):
    op_code = 0
    content_label = ""
    content_label_objname = "execution_node_bg"

    GraphicsNode_class = GraphicsExecutionNode
    NodeContent_class = ExecutionContent

    def __init__(self, scene, title, inputs=[2, 2], outputs=[1], node_type=None):
        super().__init__(scene, title, inputs, outputs, node_type=node_type)
        self._node_type = node_type
        self.value = None
        self.markDirty()

    def initSettings(self):
        super().initSettings()
        self.input_socket_position = LEFT_CENTER
        self.output_socket_position = RIGHT_CENTER

    def evalOperation(self, input1, input2):
        return 123

    def getInputSockets(self):
        """Return list of input sockets"""
        return self.inputs

    def evalImplementation(self):
        input_sockets = self.getInputSockets()
        self.markInvalid(False)
        self.markDirty(False)

        for input_socket in input_sockets:
            pass
            if len(input_socket.edges) == 0:
                pass
                input_socket.is_valid = True
                continue
            else:
                print("This socket has {} edges".format(
                    len(input_socket.edges)))

            connecting_edge = input_socket.edges[0]
            start_socket = connecting_edge.getStartSocket()
            if start_socket is None:
                pass
                input_socket.is_valid = True
            else:
                pass
            # Enum ile tip kontrolü
            input_type = getattr(input_socket, 'socket_type', None)
            start_type = getattr(start_socket, 'socket_type', None) if start_socket else None
            if isinstance(input_type, str):
                try: input_type = DataType(input_type)
                except: pass
            if isinstance(start_type, str):
                try: start_type = DataType(start_type)
                except: pass
            if input_type != start_type:
                self.markInvalid(True)
                self.markDirty(True)
                connecting_edge.is_valid = False
                input_socket.is_valid = False
            else:
                connecting_edge.is_valid = True
                input_socket.is_valid = True
        return True

    def eval(self):
        if not self.isDirty() and not self.isInvalid():
            print(" _> returning cached %s value:" %
                  self.__class__.__name__, self.value)
            return self.value

        try:

            val = self.evalImplementation()
            return val
        except ValueError as e:
            self.markInvalid()
            self.grNode.setToolTip(str(e))
            self.markDescendantsDirty()
        except Exception as e:
            self.markInvalid()
            self.grNode.setToolTip(str(e))
            dumpException(e)

    def onInputChanged(self, socket=None):
        pass
        self.markDirty()
        self.eval()

    def serialize(self):
        res = super().serialize()
        res['node_type'] = self.node_type
        return res

    def deserialize(self, data, hashmap={}, restore_id=True):
        res = super().deserialize(data, hashmap, restore_id)
        print("Deserialized ExecutionNode '%s'" %
              self.__class__.__name__, "res:", res)
        return res
