"""
Video frame cache yönetimi için ayrı modül
"""
import cv2
from PyQt5.QtGui import QPixmap, QImage


class VideoFrameCache:
    """Video frame'lerini önbelleğe alan ve yöneten sınıf"""
    
    def __init__(self, max_cache_size=10):
        self.frame_cache = {}
        self.max_cache_size = max_cache_size
        self.access_order = []  # LRU için erişim sıras<PERSON>
    
    def get_frame(self, frame_index):
        """Cache'den frame al"""
        if frame_index in self.frame_cache:
            # LRU için erişim sırasını güncelle
            if frame_index in self.access_order:
                self.access_order.remove(frame_index)
            self.access_order.append(frame_index)
            return self.frame_cache[frame_index]
        return None
    
    def add_frame(self, frame_index, pixmap):
        """Cache'e frame ekle"""
        # Cache boyutunu kontrol et
        if len(self.frame_cache) >= self.max_cache_size:
            self._remove_oldest_frame()
        
        self.frame_cache[frame_index] = pixmap
        
        # LRU için eri<PERSON><PERSON> s<PERSON>n<PERSON> güncelle
        if frame_index in self.access_order:
            self.access_order.remove(frame_index)
        self.access_order.append(frame_index)
    
    def _remove_oldest_frame(self):
        """En eski frame'i cache'den çıkar (LRU)"""
        if self.access_order:
            oldest_frame = self.access_order.pop(0)
            if oldest_frame in self.frame_cache:
                del self.frame_cache[oldest_frame]
    
    def clear(self):
        """Cache'i temizle"""
        self.frame_cache.clear()
        self.access_order.clear()
    
    def get_cache_info(self):
        """Cache bilgilerini döndür"""
        return {
            'size': len(self.frame_cache),
            'max_size': self.max_cache_size,
            'frames': list(self.frame_cache.keys())
        }
    
    def set_max_size(self, max_size):
        """Maksimum cache boyutunu ayarla"""
        self.max_cache_size = max_size
        
        # Eğer mevcut cache boyutu yeni limiti aşıyorsa, fazlalıkları temizle
        while len(self.frame_cache) > self.max_cache_size:
            self._remove_oldest_frame()


class VideoFrameLoader:
    """Video frame'lerini yükleyen ve işleyen sınıf"""
    
    @staticmethod
    def load_video_info(video_path):
        """Video bilgilerini yükle"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            cap.release()
            
            return {
                'frame_count': frame_count,
                'fps': fps,
                'width': width,
                'height': height
            }
        except Exception as e:
            return None
    
    @staticmethod
    def load_frame_at_index(video_path, frame_index, quality_reducer=None):
        """Belirli indeksteki frame'i yükle"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            # İstenen frame'e atla
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
            
            # Frame'i oku
            ret, frame = cap.read()
            if not ret:
                cap.release()
                return None
            
            # BGR'den RGB'ye dönüştür
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # QImage ve QPixmap oluştur
            h, w, ch = frame_rgb.shape
            bytes_per_line = ch * w
            q_img = QImage(frame_rgb.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_img)
            
            # Kalite düşürme işlemi varsa uygula
            if quality_reducer:
                pixmap = quality_reducer(pixmap)
            
            cap.release()
            return pixmap
            
        except Exception as e:
            return None
    
    @staticmethod
    def load_first_frame(video_path, quality_reducer=None):
        """İlk frame'i yükle"""
        return VideoFrameLoader.load_frame_at_index(video_path, 0, quality_reducer)
