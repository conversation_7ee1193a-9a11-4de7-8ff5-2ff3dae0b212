# -*- coding: utf-8 -*-
"""
Start Node Content Widget - <PERSON><PERSON><PERSON> tasar<PERSON>m
"""
import os
from typing import TYPE_CHECKING
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFileDialog, QLineEdit, QMessageBox
from qtpy.QtCore import Qt, Signal

from nodeeditor.node_empty_content import QDMEmptyContentWidget
from apps.execution_node_editor.localization import i18n

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class StartNodeContentWidget(QDMEmptyContentWidget):
    """Start node content widget - dinamik boyutlandırma ile"""

    projectPathChanged = Signal(str)

    def __init__(self, node: 'Node', parent: QWidget = None):
        self.project_path = ""
        self.project_name = ""
        self.path_button = None
        self.info_container = None
        self.name_input = None
        super().__init__(node, parent)
        self.setupUI()

    def setupUI(self):
        """UI'ı kur"""
        self.setContentMargins(6, 6, 6, 6)
        self.setContentSpacing(6)

        # Proje konumu seç butonu
        self.path_button = QPushButton(i18n.get('select_project_path'))
        self.path_button.setToolTip(i18n.get('select_project_path_tooltip'))
        self.path_button.setCursor(Qt.PointingHandCursor)
        self.path_button.setMinimumHeight(32)
        self.path_button.setStyleSheet("""
            QPushButton {
                background: rgba(70, 180, 70, 0.9);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(70, 180, 70, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: rgba(50, 150, 50, 1.0);
            }
        """)
        self.path_button.clicked.connect(self.selectProjectPath)
        self.addWidget(self.path_button)

        # Proje bilgi alanı
        self.info_container = QWidget()
        self.info_layout = QVBoxLayout()
        self.info_layout.setContentsMargins(6, 6, 6, 6)
        self.info_layout.setSpacing(2)
        self.info_container.setLayout(self.info_layout)
        self.info_container.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
        """)
        self.info_container.hide()
        self.addWidget(self.info_container)

        # Bilgi satırları
        self.path_row = self.createInfoRow(i18n.get('project_path_label'))
        self.name_row = self.createInfoRow(i18n.get('project_name_label'))

        # Value label'larını sakla
        self.path_label = self.path_row['value']
        self.name_label = self.name_row['value']

        # Proje ismi label'ına tıklanabilirlik ekle
        self.name_label.setCursor(Qt.PointingHandCursor)
        self.name_label.setToolTip(i18n.get('project_name_input_tooltip') + " (Düzenlemek için tıklayın)")
        self.name_label.mousePressEvent = self.onNameLabelClicked

        # Proje ismi giriş alanı
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText(i18n.get('project_name_input_placeholder'))
        self.name_input.setToolTip(i18n.get('project_name_input_tooltip'))
        self.name_input.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 6px 8px;
                font-size: 10px;
                margin: 2px 0px;
            }
            QLineEdit:focus {
                border: 1px solid rgba(70, 180, 70, 0.8);
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        self.name_input.textChanged.connect(self.onProjectNameChanged)
        self.name_input.returnPressed.connect(self.onNameInputFinished)
        self.name_input.editingFinished.connect(self.onNameInputFinished)
        self.name_input.hide()
        self.addWidget(self.name_input)

        # Clear butonu
        self.clear_button = QPushButton(i18n.get('clear_selection_btn'))
        self.clear_button.setToolTip(i18n.get('clear_project_tooltip'))
        self.clear_button.setMaximumHeight(20)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background: rgba(180, 70, 70, 0.8);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
            }
            QPushButton:hover {
                background: rgba(180, 70, 70, 1.0);
            }
            QPushButton:pressed {
                background: rgba(150, 50, 50, 1.0);
            }
        """)
        self.clear_button.clicked.connect(self.clearSelection)
        self.info_layout.addWidget(self.clear_button)

        self.addStretch()
        self.setMinimumSize(160, 60)

    def createInfoRow(self, label_text: str):
        """Bilgi satırı oluştur"""
        row_widget = QWidget()
        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(0, 0, 0, 0)
        row_layout.setSpacing(6)

        # Label
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 9px;
                font-weight: bold;
            }
        """)
        label.setFixedWidth(55)
        row_layout.addWidget(label)

        # Value
        value_label = QLabel("")
        value_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 9px;
            }
        """)
        value_label.setWordWrap(True)
        row_layout.addWidget(value_label)

        row_widget.setLayout(row_layout)
        self.info_layout.addWidget(row_widget)

        return {
            'widget': row_widget,
            'label': label,
            'value': value_label
        }

    def selectProjectPath(self):
        """Proje konumu seç"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            i18n.get('select_project_directory'),
            '',
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if folder_path:
            self.project_path = folder_path
            self.updateProjectInfo(folder_path)

            if self.node:
                if not hasattr(self.node, 'settings'):
                    self.node.settings = {}
                self.node.settings['project_path'] = folder_path

            self.projectPathChanged.emit(folder_path)

            # Proje konumu seçildikten sonra proje ismi girişini göster
            self.showProjectNameInput()

    def showProjectNameInput(self):
        """Proje ismi giriş alanını göster"""
        if self.name_input:
            self.name_input.show()
            self.name_input.setFocus()
            self.updateNodeGeometry()

    def onProjectNameChanged(self, text: str):
        """Proje ismi değiştiğinde çağrılır"""
        self.project_name = text.strip()

        # Node settings'e kaydet
        if self.node:
            if not hasattr(self.node, 'settings'):
                self.node.settings = {}
            self.node.settings['project_name'] = self.project_name

        # Proje bilgilerini güncelle
        if self.project_path:
            self.updateProjectInfo(self.project_path)

    def onNameLabelClicked(self, _):
        """Proje ismi label'ına tıklandığında çağrılır"""
        if self.name_input and self.project_path:
            self.name_input.show()
            self.name_input.setFocus()
            self.name_input.selectAll()
            self.updateNodeGeometry()

    def onNameInputFinished(self):
        """Proje ismi girişi tamamlandığında çağrılır"""
        if self.name_input and self.project_path:
            # Eğer proje ismi boşsa uyarı ver
            if not self.project_name.strip():
                QMessageBox.warning(
                    self,
                    "Uyarı",
                    i18n.get('project_name_required'),
                    QMessageBox.Ok
                )
                self.name_input.setFocus()
                return

            # Input'u gizle ve bilgileri güncelle
            self.name_input.hide()
            self.updateProjectInfo(self.project_path)
            self.updateNodeGeometry()

    def updateProjectInfo(self, project_path: str):
        """Proje bilgilerini güncelle"""
        if not project_path or not os.path.exists(project_path):
            self.info_container.hide()
            self.path_button.show()
            return

        try:
            # Proje adı - önce kullanıcının girdiği ismi kullan, yoksa klasör adını kullan
            if self.project_name:
                project_name = self.project_name
            else:
                project_name = os.path.basename(project_path)

            # Yolu kısalt
            if len(project_path) > 30:
                display_path = "..." + project_path[-27:]
            else:
                display_path = project_path

            # Bilgileri güncelle
            self.path_label.setText(display_path)
            self.path_label.setToolTip(project_path)
            self.name_label.setText(project_name)

            self.path_button.hide()
            self.info_container.show()

            # Node boyutunu güncelle
            self.updateNodeGeometry()

        except Exception as e:
            print(f"Error getting project info: {e}")
            self.info_container.hide()
            self.path_button.show()
            self.updateNodeGeometry()

    def clearSelection(self):
        """Seçimi temizle"""
        self.project_path = ""
        self.project_name = ""
        self.info_container.hide()
        self.path_button.show()

        # Proje ismi girişini temizle ve gizle
        if self.name_input:
            self.name_input.clear()
            self.name_input.hide()

        # Node boyutunu güncelle
        self.updateNodeGeometry()

        if self.node and hasattr(self.node, 'settings'):
            self.node.settings.pop('project_path', None)
            self.node.settings.pop('project_name', None)

    def updateNodeGeometry(self):
        """Node geometrisini güncelle"""
        if self.node and hasattr(self.node, 'grNode'):
            from qtpy.QtCore import QTimer
            QTimer.singleShot(10, self._doUpdateGeometry)

    def _doUpdateGeometry(self):
        """Gerçek geometry güncellemesi"""
        if self.node and hasattr(self.node, 'grNode'):
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
            elif hasattr(self.node.grNode, 'updateContentGeometry'):
                self.node.grNode.updateContentGeometry()

            self.updateSocketPositions()

            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle"""
        if self.node:
            for socket in getattr(self.node, 'inputs', []) + getattr(self.node, 'outputs', []):
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        if hasattr(self, 'info_container') and not self.info_container.isHidden():
            base_width = 220
            base_height = 120

            # Proje ismi girişi gösteriliyorsa yüksekliği artır
            if hasattr(self, 'name_input') and not self.name_input.isHidden():
                base_height += 35
        else:
            base_width = 180
            base_height = 80

        return QSize(base_width, base_height)

    def updateTexts(self):
        """Metinleri güncelle"""
        if hasattr(self, 'path_button') and self.path_button:
            self.path_button.setText(i18n.get('select_project_path'))
            self.path_button.setToolTip(i18n.get('select_project_path_tooltip'))
        if hasattr(self, 'clear_button') and self.clear_button:
            self.clear_button.setText(i18n.get('clear_selection_btn'))
            self.clear_button.setToolTip(i18n.get('clear_project_tooltip'))

        # Info row label'larını güncelle
        if hasattr(self, 'path_row'):
            self.path_row['label'].setText(i18n.get('project_path_label'))
        if hasattr(self, 'name_row'):
            self.name_row['label'].setText(i18n.get('project_name_label'))

        # Proje ismi girişini güncelle
        if hasattr(self, 'name_input') and self.name_input:
            self.name_input.setPlaceholderText(i18n.get('project_name_input_placeholder'))
            self.name_input.setToolTip(i18n.get('project_name_input_tooltip'))

        # Proje ismi label'ının tooltip'ini güncelle
        if hasattr(self, 'name_label') and self.name_label:
            self.name_label.setToolTip(i18n.get('project_name_input_tooltip') + " (Düzenlemek için tıklayın)")

    def serialize(self):
        """Serialize"""
        data = super().serialize()
        data.update({
            'widget_type': 'StartNodeContentWidget',
            'project_path': self.project_path,
            'project_name': self.project_name
        })
        return data

    def deserialize(self, data, hashmap={}, restore_id=True):
        """Deserialize"""
        super().deserialize(data, hashmap, restore_id)
        if 'project_path' in data and data['project_path']:
            self.project_path = data['project_path']
        if 'project_name' in data and data['project_name']:
            self.project_name = data['project_name']
            if self.name_input:
                self.name_input.setText(self.project_name)

        # Proje bilgilerini güncelle
        if self.project_path:
            self.updateProjectInfo(self.project_path)
            if self.project_name:  # Eğer proje ismi varsa input'u göster
                self.showProjectNameInput()
        return True