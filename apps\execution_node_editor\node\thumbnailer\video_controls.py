"""
Video oynatma kontrolleri için ayrı modül
"""
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtWidgets import QPushButton, QSlider, QGraphicsProxyWidget, QWidget, QVBoxLayout, QHBoxLayout, QLabel


class VideoPlayerControls(QWidget):
    """Video oynatma kontrolleri widget'ı"""
    
    # Sinyaller
    play_toggled = pyqtSignal(bool)  # True: play, False: pause
    seek_requested = pyqtSignal(float)  # 0.0 - 1.0 arası pozisyon
    volume_changed = pyqtSignal(float)  # 0.0 - 1.0 arası ses seviyesi
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_playing = False
        self.volume_level = 1.0
        self.setup_ui()
    
    def setup_ui(self):
        """UI bileşenlerini oluştur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Oynatma kontrolleri
        self._setup_playback_controls(layout)
        
        # Ses kontrolleri
        self._setup_volume_controls(layout)
    
    def _setup_playback_controls(self, parent_layout):
        """Oynatma kontrollerini oluştur"""
        controls_layout = QHBoxLayout()
        
        # Play/Pause butonu
        self.play_button = QPushButton("▶")
        self.play_button.setFixedSize(20, 20)
        self.play_button.clicked.connect(self._on_play_clicked)
        controls_layout.addWidget(self.play_button)
        
        # İlerleme çubuğu
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setRange(0, 100)
        self.progress_slider.setValue(0)
        self.progress_slider.sliderMoved.connect(self._on_seek_moved)
        self.progress_slider.sliderReleased.connect(self._on_seek_released)
        controls_layout.addWidget(self.progress_slider)
        
        parent_layout.addLayout(controls_layout)
    
    def _setup_volume_controls(self, parent_layout):
        """Ses kontrollerini oluştur"""
        volume_layout = QHBoxLayout()
        
        # Ses ikonu
        volume_label = QLabel("🔊")
        volume_layout.addWidget(volume_label)
        
        # Ses seviyesi slider'ı
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(100)
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        volume_layout.addWidget(self.volume_slider)
        
        parent_layout.addLayout(volume_layout)
    
    def _on_play_clicked(self):
        """Play/Pause butonu tıklandığında"""
        self.is_playing = not self.is_playing
        self._update_play_button()
        self.play_toggled.emit(self.is_playing)
    
    def _on_seek_moved(self):
        """İlerleme çubuğu hareket ettirildiğinde"""
        position = self.progress_slider.value() / 100.0
        self.seek_requested.emit(position)
    
    def _on_seek_released(self):
        """İlerleme çubuğu bırakıldığında"""
        # Eğer oynatılıyorsa, oynatmaya devam et sinyali gönder
        if self.is_playing:
            self.play_toggled.emit(True)
    
    def _on_volume_changed(self, value):
        """Ses seviyesi değiştiğinde"""
        self.volume_level = value / 100.0
        self.volume_changed.emit(self.volume_level)
    
    def _update_play_button(self):
        """Play butonu metnini güncelle"""
        if self.is_playing:
            self.play_button.setText("⏸")
        else:
            self.play_button.setText("▶")
    
    def set_playing(self, playing):
        """Oynatma durumunu dışarıdan ayarla"""
        if self.is_playing != playing:
            self.is_playing = playing
            self._update_play_button()
    
    def set_progress(self, progress):
        """İlerleme çubuğunu güncelle (0.0 - 1.0)"""
        value = int(progress * 100)
        self.progress_slider.setValue(value)
    
    def set_volume(self, volume):
        """Ses seviyesini ayarla (0.0 - 1.0)"""
        self.volume_level = volume
        value = int(volume * 100)
        self.volume_slider.setValue(value)
    
    def get_volume(self):
        """Mevcut ses seviyesini döndür"""
        return self.volume_level
    
    def reset(self):
        """Kontrolleri sıfırla"""
        self.is_playing = False
        self._update_play_button()
        self.progress_slider.setValue(0)


class VideoControlsProxy:
    """Video kontrollerini graphics scene'e entegre eden proxy sınıfı"""
    
    def __init__(self, parent_graphics_item):
        self.parent_item = parent_graphics_item
        self.controls_widget = VideoPlayerControls()
        self.proxy_widget = QGraphicsProxyWidget(parent_graphics_item)
        self.proxy_widget.setWidget(self.controls_widget)
        self.proxy_widget.setVisible(False)
        
        # Sinyalleri bağla
        self._connect_signals()
    
    def _connect_signals(self):
        """Kontrol sinyallerini bağla"""
        # Bu sinyaller parent thumbnailer tarafından bağlanacak
        pass
    
    def show_controls(self):
        """Kontrolleri göster"""
        self.proxy_widget.setVisible(True)
        self._update_position()
    
    def hide_controls(self):
        """Kontrolleri gizle"""
        self.proxy_widget.setVisible(False)
    
    def _update_position(self):
        """Kontrol pozisyonunu güncelle"""
        if self.parent_item:
            # Parent item'ın alt kısmına yerleştir
            parent_height = getattr(self.parent_item, 'height', 100)
            self.proxy_widget.setPos(10, parent_height - 60)
    
    def cleanup(self):
        """Kaynakları temizle"""
        if self.proxy_widget:
            self.proxy_widget.setVisible(False)
            if self.proxy_widget.scene():
                self.proxy_widget.scene().removeItem(self.proxy_widget)
            self.proxy_widget = None
        self.controls_widget = None
