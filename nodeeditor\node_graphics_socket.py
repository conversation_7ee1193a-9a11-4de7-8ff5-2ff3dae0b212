# -*- coding: utf-8 -*-
"""
A module containing Graphics representation of a :class:`~nodeeditor.node_socket.Socket`
"""
from PyQt5.QtGui import QFont, QFontMetrics, QPainterPath
from PyQt5.QtWidgets import QGraphicsRectItem, QGraphicsTextItem
from qtpy.QtWidgets import QGraphicsItem
from qtpy.QtGui import QColor, QBrush, QPen
from qtpy.QtCore import Qt, QRectF
from apps.execution_node_editor.core.node_styles import NODE_STYLES
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from nodeeditor.node_socket import Socket


class QDMGraphicsSocket(QGraphicsItem):
    """Class representing Graphic `Socket` in ``QGraphicsScene``"""
    def __init__(self, socket: 'Socket'):
        """
        :param socket: reference to :class:`~nodeeditor.node_socket.Socket`
        :type socket: :class:`~nodeeditor.node_socket.Socket`
        """
        super().__init__(socket.node.grNode)

        self.socket = socket

        self.isHighlighted = False

        self.radius = 6.0
        self.outline_width = 0.5
        self.initAssets()

    @property
    def socket_type(self):
        return self.socket.socket_type

    @property
    def socket_name(self):
        return self.socket.socket_name

    def getSocketColor(self, key):
        # Artık kullanılmıyor, fallback kaldırıldı
        return QColor("#000000")

    def changeSocketType(self):
        """Change the Socket Type"""
        self._color_background = self.getSocketColor(self.socket_type)
        self._brush = QBrush(self._color_background)
        # print("Socket changed to:", self._color_background.getRgbF())
        self.update()

    def initAssets(self):
        node = getattr(self.socket, 'node', None)
        style = getattr(node, 'style', {})
        if isinstance(style, str):
            style = {'theme': style}
        theme = style.get('theme', 'green_theme')
        theme_style = NODE_STYLES.get(theme, NODE_STYLES['green_theme'])
        color_hex = theme_style.get('port', '#ff00ff')
        highlight_hex = theme_style.get('highlight', color_hex)
        self._color_background = QColor(color_hex)
        self._color_outline = QColor("#FFFFFF")
        self._color_highlight = QColor(highlight_hex)
        self._color_invalid = QColor("#ed8836")
        self._pen = QPen(self._color_outline)
        self._pen.setWidthF(self.outline_width)
        self._pen_highlight = QPen(QColor('#FFFFFF'))
        self._pen_highlight.setWidthF(1.0)
        self._brush = QBrush(self._color_background)
        self._brush_highlight = QBrush(self._color_highlight)
        self._brush_invalid = QBrush(self._color_invalid)
        self.path_title = QPainterPath()
        self.path_title.setFillRule(Qt.WindingFill)
        self._brush_title = QBrush(QColor("#000000"))

    def paint(self, painter, QStyleOptionGraphicsItem, widget=None):
        node = getattr(self.socket, 'node', None)
        style = getattr(node, 'style', None)
        if self.socket.is_valid:
            if self.isHighlighted:
                painter.setBrush(self._brush_highlight)
                painter.setPen(self._pen_highlight)
            else:
                painter.setBrush(self._brush)
                painter.setPen(self._pen)
            painter.drawEllipse(int(-self.radius), int(-self.radius), int(2 * self.radius), int(2 * self.radius))
            painter.setPen(Qt.NoPen)
        else:
            painter.setBrush(self._brush_invalid)
            painter.setPen(self._pen if not self.isHighlighted else self._pen_highlight)
            painter.drawEllipse(int(-self.radius), int(-self.radius), int(2 * self.radius), int(2 * self.radius))
            painter.setPen(Qt.NoPen)

        # Socket label'ını socket renginde ve üstünde göster
        if hasattr(self.socket, 'label') and self.socket.label:
            # Label'ı localize et - i18n sistemini kullan
            try:
                from apps.execution_node_editor.localization import i18n
                label_text = i18n.get(self.socket.label)
            except:
                label_text = self.socket.label
        elif self.socket.is_input:
            try:
                from apps.execution_node_editor.localization import i18n
                label_text = i18n.get('input_label')
            except:
                label_text = 'Input'
        else:
            try:
                from apps.execution_node_editor.localization import i18n
                label_text = i18n.get('output_label')
            except:
                label_text = 'Output'

        # Daha küçük font
        font = QFont("Roboto", 7, QFont.Bold)  # 8'den 7'ye düşürdük
        painter.setFont(font)
        fm = QFontMetrics(font)
        text_width = fm.width(label_text)
        text_height = fm.height()

        # Socket'ın üstünde konumlandır
        x = -text_width // 2  # Ortalanmış
        y = -self.radius - 4  # Socket'ın üstünde (6'dan 4'e düşürdük)

        # Socket renginde çiz
        if self.isHighlighted:
            painter.setPen(self._color_highlight)
        else:
            painter.setPen(self._color_background)

        painter.drawText(int(x), int(y), label_text)

    def boundingRect(self) -> QRectF:
        """Defining Qt' bounding rectangle - küçük label için optimize edildi"""
        # Küçük label için daha az alan
        label_height = 12  # Daha küçük font için
        label_width = 35   # Daha kompakt

        return QRectF(
            - max(self.radius + self.outline_width, label_width // 2),
            - self.radius - self.outline_width - label_height,
            max(2 * (self.radius + self.outline_width), label_width),
            2 * (self.radius + self.outline_width) + label_height,
        )

    def isInRadius(self, scenePos, radius):
        # Soketin sahnedeki merkez pozisyonunu al
        center = self.scenePos()
        dx = center.x() - scenePos.x()
        dy = center.y() - scenePos.y()
        return (dx*dx + dy*dy) <= (radius*radius)