# -*- coding: utf-8 -*-
"""
End Node Content Widget - <PERSON><PERSON>k tasarım
"""
import os
from typing import TYPE_CHECKING
from qtpy.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from qtpy.QtCore import Qt, QTimer

from nodeeditor.node_empty_content import QDMEmptyContentWidget
from apps.execution_node_editor.localization import i18n

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class EndNodeContentWidget(QDMEmptyContentWidget):
    """End node content widget - dinamik boyutlandırma ile"""

    def __init__(self, node: 'Node', parent: QWidget = None):
        self.project_name = ""
        self.project_path = ""
        self.connected_nodes = []
        self.update_timer = None
        super().__init__(node, parent)
        self.setupUI()
        self.startConnectionMonitoring()

    def setupUI(self):
        """UI'ı kur"""
        self.setContentMargins(8, 8, 8, 8)
        self.setContentSpacing(4)

        # Proje ismi label'ı
        self.project_label = QLabel(i18n.get('no_project_connected'))
        self.project_label.setAlignment(Qt.AlignCenter)
        self.project_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                background: rgba(0, 0, 0, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                padding: 4px 8px;
                margin: 2px 0px;
            }
        """)
        self.addWidget(self.project_label)

        # Bağlı düğümler başlığı
        self.nodes_title = QLabel(i18n.get('connected_nodes_label'))
        self.nodes_title.setAlignment(Qt.AlignCenter)
        self.nodes_title.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-weight: bold;
                font-size: 10px;
                margin: 4px 0px 2px 0px;
            }
        """)
        self.addWidget(self.nodes_title)

        # Düğüm listesi container
        self.nodes_container = QWidget()
        self.nodes_layout = QVBoxLayout()
        self.nodes_layout.setContentsMargins(4, 4, 4, 4)
        self.nodes_layout.setSpacing(2)
        self.nodes_container.setLayout(self.nodes_layout)
        self.nodes_container.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
        """)
        self.addWidget(self.nodes_container)

        # Nihai çıktı label'ı
        self.final_label = QLabel(i18n.get('final_output_label'))
        self.final_label.setAlignment(Qt.AlignCenter)
        self.final_label.setStyleSheet("""
            QLabel {
                color: #ffaa00;
                font-weight: bold;
                font-size: 10px;
                background: rgba(255, 170, 0, 0.1);
                border: 1px solid rgba(255, 170, 0, 0.3);
                border-radius: 4px;
                padding: 4px 8px;
                margin: 2px 0px;
            }
        """)
        self.addWidget(self.final_label)

        # Klasör oluştur butonu
        self.create_folders_btn = QPushButton(i18n.get('create_folders_btn'))
        self.create_folders_btn.setToolTip(i18n.get('create_folders_tooltip'))
        self.create_folders_btn.setStyleSheet("""
            QPushButton {
                background: rgba(70, 180, 70, 0.8);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 10px;
                font-weight: bold;
                margin: 4px 0px;
            }
            QPushButton:hover {
                background: rgba(70, 180, 70, 1.0);
            }
            QPushButton:pressed {
                background: rgba(50, 150, 50, 1.0);
            }
            QPushButton:disabled {
                background: rgba(100, 100, 100, 0.5);
                color: rgba(255, 255, 255, 0.5);
            }
        """)
        self.create_folders_btn.clicked.connect(self.createProjectFolders)
        self.create_folders_btn.setEnabled(False)
        self.addWidget(self.create_folders_btn)

        self.addStretch()
        self.setMinimumSize(200, 150)

        # İlk geometry update'i
        QTimer.singleShot(100, self.initialUpdate)

    def initialUpdate(self):
        """İlk güncelleme - node oluştuğunda çağrılır"""
        self.updateConnections()
        self.updateGeometry()
        self.updateNodeGeometry()

    def startConnectionMonitoring(self):
        """Bağlantı izlemeyi başlat"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.updateConnections)
        self.update_timer.start(1000)  # Her saniye kontrol et

    def updateConnections(self):
        """Bağlantıları güncelle"""
        if not self.node or not hasattr(self.node, 'inputs') or not self.node.inputs:
            return

        # Start node'u bul ve proje bilgilerini al
        start_node = self.findStartNode()
        if start_node:
            self.updateProjectInfo(start_node)

        # Bağlı node'ları bul
        connected_nodes = self.findConnectedNodes()
        if connected_nodes != self.connected_nodes:
            self.connected_nodes = connected_nodes
            self.updateNodesList()

    def findStartNode(self):
        """Start node'u bul"""
        if not self.node or not self.node.scene:
            return None

        for node in self.node.scene.nodes:
            if hasattr(node, 'node_type') and node.node_type == 'StartNode':
                return node
        return None

    def updateProjectInfo(self, start_node):
        """Start node'dan proje bilgilerini al"""
        if not start_node or not hasattr(start_node, 'content'):
            self.project_name = ""
            self.project_path = ""
            self.project_label.setText(i18n.get('no_project_connected'))
            self.create_folders_btn.setEnabled(False)
            return

        content = start_node.content
        if hasattr(content, 'project_name') and hasattr(content, 'project_path'):
            self.project_name = content.project_name or ""
            self.project_path = content.project_path or ""
            
            if self.project_name and self.project_path:
                self.project_label.setText(f"📁 {self.project_name}")
                self.create_folders_btn.setEnabled(True)
            else:
                self.project_label.setText(i18n.get('no_project_connected'))
                self.create_folders_btn.setEnabled(False)
            
            # Proje bilgileri değiştiğinde boyutu güncelle
            self.updateGeometry()
            self.updateNodeGeometry()

    def findConnectedNodes(self):
        """Bağlı node'ları bul - tüm pipeline'ı tarar"""
        if not self.node or not self.node.scene:
            return []

        # Tüm scene'deki node'ları al
        all_nodes = []
        for node in self.node.scene.nodes:
            if hasattr(node, 'node_type') and node.node_type not in ['StartNode', 'EndNode']:
                all_nodes.append({
                    'title': getattr(node, 'title', 'Unknown'),
                    'type': node.node_type,
                    'id': id(node),
                    'node': node
                })

        # Start node'dan başlayarak erişilebilir node'ları bul
        start_node = self.findStartNode()
        if not start_node:
            return []

        connected_nodes = []
        visited = set()

        def traverse_from_node(current_node):
            if not current_node or id(current_node) in visited:
                return
            visited.add(id(current_node))

            # Bu node'u listeye ekle (start ve end node hariç)
            if hasattr(current_node, 'node_type'):
                if current_node.node_type not in ['StartNode', 'EndNode']:
                    node_info = {
                        'title': getattr(current_node, 'title', 'Unknown'),
                        'type': current_node.node_type,
                        'id': id(current_node)
                    }
                    if node_info not in connected_nodes:
                        connected_nodes.append(node_info)

            # Output socket'lardan ileriye doğru takip et
            if hasattr(current_node, 'outputs'):
                for output_socket in current_node.outputs:
                    if hasattr(output_socket, 'edges'):
                        for edge in output_socket.edges:
                            if hasattr(edge, 'end_socket') and edge.end_socket:
                                if hasattr(edge.end_socket, 'node'):
                                    traverse_from_node(edge.end_socket.node)

        # Start node'dan başlayarak ileriye doğru traverse et
        traverse_from_node(start_node)

        # Pipeline sırasına göre sırala
        ordered_nodes = self.orderNodesByPipeline(connected_nodes, start_node)
        return ordered_nodes

    def orderNodesByPipeline(self, nodes, start_node):
        """Node'ları pipeline sırasına göre sırala"""
        if not nodes:
            return []

        # Her node'un start node'dan uzaklığını hesapla
        node_distances = {}

        def calculate_distance(current_node, distance=0, visited=None):
            if visited is None:
                visited = set()

            if id(current_node) in visited:
                return
            visited.add(id(current_node))

            # Bu node'un mesafesini kaydet
            node_id = id(current_node)
            if node_id not in node_distances or node_distances[node_id] > distance:
                node_distances[node_id] = distance

            # Output'lardan devam et
            if hasattr(current_node, 'outputs'):
                for output_socket in current_node.outputs:
                    if hasattr(output_socket, 'edges'):
                        for edge in output_socket.edges:
                            if hasattr(edge, 'end_socket') and edge.end_socket:
                                if hasattr(edge.end_socket, 'node'):
                                    calculate_distance(edge.end_socket.node, distance + 1, visited.copy())

        # Start node'dan mesafeleri hesapla
        calculate_distance(start_node)

        # Node'ları mesafelerine göre sırala
        sorted_nodes = sorted(nodes, key=lambda n: node_distances.get(n['id'], 999))
        return sorted_nodes

    def updateNodesList(self):
        """Node listesini güncelle"""
        # Mevcut widget'ları temizle
        for i in reversed(range(self.nodes_layout.count())):
            child = self.nodes_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.connected_nodes:
            no_nodes_label = QLabel(i18n.get('no_nodes_connected'))
            no_nodes_label.setAlignment(Qt.AlignCenter)
            no_nodes_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 9px;
                    font-style: italic;
                    padding: 4px;
                }
            """)
            self.nodes_layout.addWidget(no_nodes_label)
        else:
            for i, node_info in enumerate(self.connected_nodes, 1):
                node_label = QLabel(f"{i}. {node_info['title']}")
                node_label.setStyleSheet("""
                    QLabel {
                        color: rgba(255, 255, 255, 0.9);
                        font-size: 9px;
                        padding: 2px 4px;
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 2px;
                        margin: 1px;
                    }
                """)
                self.nodes_layout.addWidget(node_label)

        # Widget boyutunu güncelle
        self.updateGeometry()
        # Node boyutunu güncelle
        self.updateNodeGeometry()

    def createProjectFolders(self):
        """Proje klasörlerini oluştur"""
        if not self.project_path or not self.project_name:
            return

        try:
            # Ana proje klasörü
            project_dir = os.path.join(self.project_path, self.project_name)
            os.makedirs(project_dir, exist_ok=True)

            # Her node için klasör oluştur
            for i, node_info in enumerate(self.connected_nodes, 1):
                node_folder = f"{i:02d}_{node_info['title'].replace(' ', '_')}"
                node_path = os.path.join(project_dir, node_folder)
                os.makedirs(node_path, exist_ok=True)

            # Nihai çıktı klasörü
            final_path = os.path.join(project_dir, "00_Final")
            os.makedirs(final_path, exist_ok=True)

            QMessageBox.information(
                self,
                "Başarılı",
                i18n.get('folders_created_msg'),
                QMessageBox.Ok
            )

        except Exception as e:
            QMessageBox.warning(
                self,
                "Hata",
                f"Klasörler oluşturulurken hata: {str(e)}",
                QMessageBox.Ok
            )

    def updateNodeGeometry(self):
        """Node geometrisini güncelle"""
        if self.node and hasattr(self.node, 'grNode'):
            from qtpy.QtCore import QTimer
            QTimer.singleShot(10, self._doUpdateGeometry)

    def _doUpdateGeometry(self):
        """Gerçek geometry güncellemesi"""
        if self.node and hasattr(self.node, 'grNode'):
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
            elif hasattr(self.node.grNode, 'updateContentGeometry'):
                self.node.grNode.updateContentGeometry()

            self.updateSocketPositions()

            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle"""
        if self.node:
            for socket in getattr(self.node, 'inputs', []) + getattr(self.node, 'outputs', []):
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        base_width = 220
        base_height = 180  # Temel yükseklik

        # Proje ismi varsa yükseklik ekle
        if self.project_name:
            base_height += 10

        # Node sayısına göre yüksekliği artır
        if self.connected_nodes:
            # Her node için 22 piksel (label + margin)
            base_height += len(self.connected_nodes) * 22
        else:
            # "Bağlı düğüm yok" mesajı için alan
            base_height += 25

        # Minimum ve maksimum sınırlar
        base_height = max(180, min(base_height, 500))

        return QSize(base_width, base_height)

    def updateTexts(self):
        """Metinleri güncelle"""
        if hasattr(self, 'project_label'):
            if not self.project_name:
                self.project_label.setText(i18n.get('no_project_connected'))
        if hasattr(self, 'nodes_title'):
            self.nodes_title.setText(i18n.get('connected_nodes_label'))
        if hasattr(self, 'final_label'):
            self.final_label.setText(i18n.get('final_output_label'))
        if hasattr(self, 'create_folders_btn'):
            self.create_folders_btn.setText(i18n.get('create_folders_btn'))
            self.create_folders_btn.setToolTip(i18n.get('create_folders_tooltip'))

    def serialize(self):
        """Serialize"""
        data = super().serialize()
        data.update({
            'widget_type': 'EndNodeContentWidget',
            'project_name': self.project_name,
            'project_path': self.project_path
        })
        return data

    def deserialize(self, data, hashmap={}, restore_id=True):
        """Deserialize"""
        super().deserialize(data, hashmap, restore_id)
        if 'project_name' in data:
            self.project_name = data['project_name']
        if 'project_path' in data:
            self.project_path = data['project_path']
        return True

    def __del__(self):
        """Destructor - timer'ı temizle"""
        if hasattr(self, 'update_timer') and self.update_timer:
            self.update_timer.stop()
            self.update_timer = None
