import os
import json
import locale
from PyQt5.QtCore import QObject, pyqtSignal

class LocalizationManager(QObject):
    languageChanged = pyqtSignal(str)

    SUPPORTED_LANGUAGES = ["en", "tr"]
    DEFAULT_LANGUAGE = "en"
    LOCALES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../locales')

    # Translation file categories
    TRANSLATION_FILES = [
        'common.json',      # <PERSON><PERSON> terimler (OK, Cancel, Apply vb.)
        'menu.json',        # <PERSON><PERSON> öğeleri
        'actions.json',     # <PERSON>ylemler ve kısayollar
        'nodes.json',       # Node ile ilgili terimler
        'ui.json',          # UI bileşenleri
        'messages.json',    # Mesaj kutuları ve bildirimler
        'shortcuts.json',   # Kısayol tuşları ve kategorileri
        'tooltips.json'     # Tooltip metinleri
    ]

    def __init__(self):
        super().__init__()
        self._language = None
        self._translations = {}
        self.set_language(self.detect_system_language())

    def detect_system_language(self):
        lang, _ = locale.getdefaultlocale()
        if lang:
            lang_code = lang.split('_')[0]
            if lang_code in self.SUPPORTED_LANGUAGES:
                return lang_code
        return self.DEFAULT_LANGUAGE

    def set_language(self, lang):
        if lang not in self.SUPPORTED_LANGUAGES:
            lang = self.DEFAULT_LANGUAGE
        if self._language == lang:
            return
        self._language = lang
        self._translations = self._load_translations(lang)
        self.languageChanged.emit(lang)

    def get_language(self):
        return self._language

    def _load_translations(self, lang):
        """Load translations from organized structure or fallback to legacy files"""
        translations = {}

        # Try to load from organized structure first (lang/category.json)
        lang_dir = os.path.join(self.LOCALES_DIR, lang)
        if os.path.exists(lang_dir):
            for file_name in self.TRANSLATION_FILES:
                file_path = os.path.join(lang_dir, file_name)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            category_translations = json.load(f)
                            translations.update(category_translations)
                    except Exception as e:
                        pass

        # If organized structure doesn't exist or is incomplete, fallback to legacy file
        if not translations:
            legacy_path = os.path.join(self.LOCALES_DIR, f"{lang}.json")
            try:
                with open(legacy_path, "r", encoding="utf-8") as f:
                    translations = json.load(f)
            except Exception:
                if lang != self.DEFAULT_LANGUAGE:
                    return self._load_translations(self.DEFAULT_LANGUAGE)
                return {}

        return translations

    def get(self, key):
        """Get translation for a key"""
        return self._translations.get(key, key)

    def get_available_languages(self):
        """Get list of available languages with their metadata"""
        languages = []
        for lang in self.SUPPORTED_LANGUAGES:
            lang_dir = os.path.join(self.LOCALES_DIR, lang)
            index_file = os.path.join(lang_dir, 'index.json')

            if os.path.exists(index_file):
                try:
                    with open(index_file, "r", encoding="utf-8") as f:
                        index_data = json.load(f)
                        meta = index_data.get('_meta', {})
                        languages.append({
                            'code': lang,
                            'name': meta.get('language_name', lang.upper()),
                            'version': meta.get('version', '1.0.0'),
                            'description': meta.get('description', '')
                        })
                except Exception:
                    # Fallback to simple language info
                    languages.append({
                        'code': lang,
                        'name': self.get(f'language_{lang}') if self._translations else lang.upper(),
                        'version': '1.0.0',
                        'description': ''
                    })
            else:
                # Legacy support
                languages.append({
                    'code': lang,
                    'name': self.get(f'language_{lang}') if self._translations else lang.upper(),
                    'version': '1.0.0',
                    'description': 'Legacy format'
                })
        return languages

    def get_translation_stats(self):
        """Get statistics about current translations"""
        return {
            'language': self._language,
            'total_keys': len(self._translations),
            'structure': 'organized' if os.path.exists(os.path.join(self.LOCALES_DIR, self._language)) else 'legacy'
        }

# Singleton instance
i18n = LocalizationManager()