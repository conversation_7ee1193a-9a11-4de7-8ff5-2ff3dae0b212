# -*- coding: utf-8 -*-
"""
Node Property System - Dinamik property yönetimi için sistem
"""
from typing import Any, Dict, List, Optional, Callable, Union
from enum import Enum
from qtpy.QtCore import QObject, Signal, QTimer
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox, QSlider, QTextEdit
from qtpy.QtCore import Qt
from nodeeditor.node_serializable import Serializable


class PropertyType(Enum):
    """Property türleri"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ENUM = "enum"
    COLOR = "color"
    FILE_PATH = "file_path"
    RANGE = "range"
    TEXT = "text"


class PropertyDefinition:
    """Property tanımı sınıfı"""
    
    def __init__(self, 
                 name: str,
                 prop_type: PropertyType,
                 default_value: Any = None,
                 display_name: str = None,
                 description: str = "",
                 min_value: float = None,
                 max_value: float = None,
                 enum_values: List[str] = None,
                 readonly: bool = False,
                 visible: bool = True,
                 validator: Callable = None):
        """
        Property tanımı
        
        :param name: Property adı (kod içinde kullanılacak)
        :param prop_type: Property türü
        :param default_value: Varsayılan değer
        :param display_name: UI'da gösterilecek isim
        :param description: Açıklama/tooltip
        :param min_value: Minimum değer (sayısal türler için)
        :param max_value: Maksimum değer (sayısal türler için)
        :param enum_values: Enum seçenekleri
        :param readonly: Salt okunur mu
        :param visible: Görünür mü
        :param validator: Özel validation fonksiyonu
        """
        self.name = name
        self.prop_type = prop_type
        self.default_value = default_value
        self.display_name = display_name or name.replace('_', ' ').title()
        self.description = description
        self.min_value = min_value
        self.max_value = max_value
        self.enum_values = enum_values or []
        self.readonly = readonly
        self.visible = visible
        self.validator = validator


class PropertyValue:
    """Property değeri wrapper sınıfı"""
    
    def __init__(self, definition: PropertyDefinition, value: Any = None):
        self.definition = definition
        self._value = value if value is not None else definition.default_value
        self._callbacks = []
    
    @property
    def value(self):
        return self._value
    
    @value.setter
    def value(self, new_value):
        # Validation
        if self.definition.validator and not self.definition.validator(new_value):
            raise ValueError(f"Invalid value for property {self.definition.name}: {new_value}")
        
        # Type checking ve range kontrolü
        if self.definition.prop_type == PropertyType.INTEGER:
            new_value = int(new_value)
            if self.definition.min_value is not None and new_value < self.definition.min_value:
                new_value = self.definition.min_value
            if self.definition.max_value is not None and new_value > self.definition.max_value:
                new_value = self.definition.max_value
        elif self.definition.prop_type == PropertyType.FLOAT:
            new_value = float(new_value)
            if self.definition.min_value is not None and new_value < self.definition.min_value:
                new_value = self.definition.min_value
            if self.definition.max_value is not None and new_value > self.definition.max_value:
                new_value = self.definition.max_value
        elif self.definition.prop_type == PropertyType.BOOLEAN:
            new_value = bool(new_value)
        elif self.definition.prop_type == PropertyType.STRING:
            new_value = str(new_value)
        
        old_value = self._value
        self._value = new_value
        
        # Callback'leri çağır
        for callback in self._callbacks:
            callback(old_value, new_value)
    
    def add_change_callback(self, callback: Callable):
        """Değer değişikliği callback'i ekle"""
        self._callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable):
        """Değer değişikliği callback'ini kaldır"""
        if callback in self._callbacks:
            self._callbacks.remove(callback)


class PropertyManager(QObject, Serializable):
    """Property yönetici sınıfı"""
    
    # Signals
    propertyChanged = Signal(str, object, object)  # property_name, old_value, new_value
    propertiesChanged = Signal()  # Herhangi bir property değiştiğinde
    
    def __init__(self):
        super().__init__()
        self._properties: Dict[str, PropertyValue] = {}
        self._definitions: Dict[str, PropertyDefinition] = {}
        self._change_timer = QTimer()
        self._change_timer.setSingleShot(True)
        self._change_timer.timeout.connect(self._emit_properties_changed)
        self._pending_changes = False
    
    def define_property(self, definition: PropertyDefinition):
        """Property tanımı ekle"""
        self._definitions[definition.name] = definition
        if definition.name not in self._properties:
            prop_value = PropertyValue(definition)
            prop_value.add_change_callback(self._on_property_changed)
            self._properties[definition.name] = prop_value
    
    def define_properties(self, definitions: List[PropertyDefinition]):
        """Birden fazla property tanımı ekle"""
        for definition in definitions:
            self.define_property(definition)
    
    def get_property(self, name: str) -> Any:
        """Property değerini al"""
        if name in self._properties:
            return self._properties[name].value
        return None
    
    def set_property(self, name: str, value: Any):
        """Property değerini ayarla"""
        if name in self._properties:
            self._properties[name].value = value
    
    def get_property_definition(self, name: str) -> Optional[PropertyDefinition]:
        """Property tanımını al"""
        return self._definitions.get(name)
    
    def get_all_properties(self) -> Dict[str, Any]:
        """Tüm property değerlerini al"""
        return {name: prop.value for name, prop in self._properties.items()}
    
    def get_visible_properties(self) -> Dict[str, PropertyValue]:
        """Görünür property'leri al"""
        return {name: prop for name, prop in self._properties.items() 
                if self._definitions[name].visible}
    
    def _on_property_changed(self, old_value, new_value):
        """Property değişikliği callback'i"""
        # Hangi property değişti bul
        for name, prop in self._properties.items():
            if prop.value == new_value:
                self.propertyChanged.emit(name, old_value, new_value)
                break
        
        # Toplu değişiklik sinyali için timer başlat
        self._pending_changes = True
        self._change_timer.start(50)  # 50ms debounce
    
    def _emit_properties_changed(self):
        """Toplu property değişikliği sinyali gönder"""
        if self._pending_changes:
            self.propertiesChanged.emit()
            self._pending_changes = False
    
    def serialize(self) -> dict:
        """Property'leri serialize et"""
        return {
            'properties': {name: prop.value for name, prop in self._properties.items()},
            'definitions': {name: {
                'name': defn.name,
                'prop_type': defn.prop_type.value,
                'default_value': defn.default_value,
                'display_name': defn.display_name,
                'description': defn.description,
                'min_value': defn.min_value,
                'max_value': defn.max_value,
                'enum_values': defn.enum_values,
                'readonly': defn.readonly,
                'visible': defn.visible
            } for name, defn in self._definitions.items()}
        }
    
    def deserialize(self, data: dict, hashmap: dict = {}, restore_id: bool = True):
        """Property'leri deserialize et"""
        if 'definitions' in data:
            for name, defn_data in data['definitions'].items():
                definition = PropertyDefinition(
                    name=defn_data['name'],
                    prop_type=PropertyType(defn_data['prop_type']),
                    default_value=defn_data.get('default_value'),
                    display_name=defn_data.get('display_name'),
                    description=defn_data.get('description', ''),
                    min_value=defn_data.get('min_value'),
                    max_value=defn_data.get('max_value'),
                    enum_values=defn_data.get('enum_values', []),
                    readonly=defn_data.get('readonly', False),
                    visible=defn_data.get('visible', True)
                )
                self.define_property(definition)
        
        if 'properties' in data:
            for name, value in data['properties'].items():
                if name in self._properties:
                    self._properties[name].value = value
        
        return True


class PropertyWidget(QWidget):
    """Property için UI widget base sınıfı"""

    valueChanged = Signal(object)  # Değer değiştiğinde emit edilir

    def __init__(self, property_value: PropertyValue, parent=None):
        super().__init__(parent)
        self.property_value = property_value
        self.definition = property_value.definition
        self._updating = False
        self.initUI()
        self.connectSignals()
        self.updateValue()

    def initUI(self):
        """UI'ı başlat - alt sınıflarda override edilmeli"""
        pass

    def connectSignals(self):
        """Signal'ları bağla - alt sınıflarda override edilmeli"""
        pass

    def updateValue(self):
        """Widget'ın değerini property'den güncelle - alt sınıflarda override edilmeli"""
        pass

    def getValue(self):
        """Widget'tan değeri al - alt sınıflarda override edilmeli"""
        return None

    def setValue(self, value):
        """Widget'a değer set et - alt sınıflarda override edilmeli"""
        pass

    def _on_widget_changed(self):
        """Widget değiştiğinde çağrılır"""
        if not self._updating:
            new_value = self.getValue()
            self.property_value.value = new_value
            self.valueChanged.emit(new_value)


class StringPropertyWidget(PropertyWidget):
    """String property için widget"""

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        self.label = QLabel(self.definition.display_name)
        self.line_edit = QLineEdit()
        self.line_edit.setPlaceholderText(self.definition.description)
        self.line_edit.setReadOnly(self.definition.readonly)

        layout.addWidget(self.label)
        layout.addWidget(self.line_edit)
        self.setLayout(layout)

    def connectSignals(self):
        self.line_edit.textChanged.connect(self._on_widget_changed)

    def updateValue(self):
        self._updating = True
        self.line_edit.setText(str(self.property_value.value or ""))
        self._updating = False

    def getValue(self):
        return self.line_edit.text()

    def setValue(self, value):
        self.line_edit.setText(str(value))


class IntegerPropertyWidget(PropertyWidget):
    """Integer property için widget"""

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        self.label = QLabel(self.definition.display_name)
        self.spin_box = QSpinBox()

        if self.definition.min_value is not None:
            self.spin_box.setMinimum(int(self.definition.min_value))
        else:
            self.spin_box.setMinimum(-2147483648)

        if self.definition.max_value is not None:
            self.spin_box.setMaximum(int(self.definition.max_value))
        else:
            self.spin_box.setMaximum(2147483647)

        self.spin_box.setReadOnly(self.definition.readonly)
        self.spin_box.setToolTip(self.definition.description)

        layout.addWidget(self.label)
        layout.addWidget(self.spin_box)
        self.setLayout(layout)

    def connectSignals(self):
        self.spin_box.valueChanged.connect(self._on_widget_changed)

    def updateValue(self):
        self._updating = True
        self.spin_box.setValue(int(self.property_value.value or 0))
        self._updating = False

    def getValue(self):
        return self.spin_box.value()

    def setValue(self, value):
        self.spin_box.setValue(int(value))


class FloatPropertyWidget(PropertyWidget):
    """Float property için widget"""

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        self.label = QLabel(self.definition.display_name)
        self.double_spin_box = QDoubleSpinBox()

        if self.definition.min_value is not None:
            self.double_spin_box.setMinimum(float(self.definition.min_value))
        else:
            self.double_spin_box.setMinimum(-999999.0)

        if self.definition.max_value is not None:
            self.double_spin_box.setMaximum(float(self.definition.max_value))
        else:
            self.double_spin_box.setMaximum(999999.0)

        self.double_spin_box.setDecimals(3)
        self.double_spin_box.setReadOnly(self.definition.readonly)
        self.double_spin_box.setToolTip(self.definition.description)

        layout.addWidget(self.label)
        layout.addWidget(self.double_spin_box)
        self.setLayout(layout)

    def connectSignals(self):
        self.double_spin_box.valueChanged.connect(self._on_widget_changed)

    def updateValue(self):
        self._updating = True
        self.double_spin_box.setValue(float(self.property_value.value or 0.0))
        self._updating = False

    def getValue(self):
        return self.double_spin_box.value()

    def setValue(self, value):
        self.double_spin_box.setValue(float(value))


class BooleanPropertyWidget(PropertyWidget):
    """Boolean property için widget"""

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        self.checkbox = QCheckBox(self.definition.display_name)
        self.checkbox.setEnabled(not self.definition.readonly)
        self.checkbox.setToolTip(self.definition.description)

        layout.addWidget(self.checkbox)
        self.setLayout(layout)

    def connectSignals(self):
        self.checkbox.toggled.connect(self._on_widget_changed)

    def updateValue(self):
        self._updating = True
        self.checkbox.setChecked(bool(self.property_value.value))
        self._updating = False

    def getValue(self):
        return self.checkbox.isChecked()

    def setValue(self, value):
        self.checkbox.setChecked(bool(value))


class EnumPropertyWidget(PropertyWidget):
    """Enum property için widget"""

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        self.label = QLabel(self.definition.display_name)
        self.combo_box = QComboBox()

        for value in self.definition.enum_values:
            self.combo_box.addItem(str(value))

        self.combo_box.setEnabled(not self.definition.readonly)
        self.combo_box.setToolTip(self.definition.description)

        layout.addWidget(self.label)
        layout.addWidget(self.combo_box)
        self.setLayout(layout)

    def connectSignals(self):
        self.combo_box.currentTextChanged.connect(self._on_widget_changed)

    def updateValue(self):
        self._updating = True
        current_value = str(self.property_value.value or "")
        index = self.combo_box.findText(current_value)
        if index >= 0:
            self.combo_box.setCurrentIndex(index)
        self._updating = False

    def getValue(self):
        return self.combo_box.currentText()

    def setValue(self, value):
        index = self.combo_box.findText(str(value))
        if index >= 0:
            self.combo_box.setCurrentIndex(index)


class PropertyWidgetFactory:
    """Property widget factory sınıfı"""

    @staticmethod
    def create_widget(property_value: PropertyValue) -> PropertyWidget:
        """Property türüne göre uygun widget oluştur"""
        prop_type = property_value.definition.prop_type

        if prop_type == PropertyType.STRING:
            return StringPropertyWidget(property_value)
        elif prop_type == PropertyType.INTEGER:
            return IntegerPropertyWidget(property_value)
        elif prop_type == PropertyType.FLOAT:
            return FloatPropertyWidget(property_value)
        elif prop_type == PropertyType.BOOLEAN:
            return BooleanPropertyWidget(property_value)
        elif prop_type == PropertyType.ENUM:
            return EnumPropertyWidget(property_value)
        else:
            # Fallback olarak string widget kullan
            return StringPropertyWidget(property_value)
