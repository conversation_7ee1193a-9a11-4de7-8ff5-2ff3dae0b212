# -*- coding: utf-8 -*-
"""
Node Geometry Observer System - Node boyut değişikliklerini izlemek için observer pattern
"""
from typing import List, Callable, TYPE_CHECKING
from qtpy.QtCore import QObject, Signal, QTimer
from abc import ABC, abstractmethod

if TYPE_CHECKING:
    from nodeeditor.node_node import Node
    from nodeeditor.node_graphics_node import QDMGraphicsNode


class GeometryObserver(ABC):
    """Geometry değişikliklerini izleyen observer interface"""
    
    @abstractmethod
    def onGeometryChanged(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry değiştiğinde çağrılır"""
        pass
    
    @abstractmethod
    def onContentSizeChanged(self, node: 'Node', content_size: tuple):
        """Content boyutu değiştiğinde çağrılır"""
        pass


class NodeGeometryManager(QObject):
    """Node geometry değişikliklerini yöneten manager sınıfı"""
    
    # Signals
    geometryChanged = Signal(object, tuple, tuple)  # node, old_size, new_size
    contentSizeChanged = Signal(object, tuple)  # node, content_size
    
    def __init__(self):
        super().__init__()
        self._observers: List[GeometryObserver] = []
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self._process_pending_updates)
        self._pending_updates = {}
        
        # Connect signals to observer notifications
        self.geometryChanged.connect(self._notify_geometry_observers)
        self.contentSizeChanged.connect(self._notify_content_observers)
    
    def addObserver(self, observer: GeometryObserver):
        """Observer ekle"""
        if observer not in self._observers:
            self._observers.append(observer)
    
    def removeObserver(self, observer: GeometryObserver):
        """Observer kaldır"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notifyGeometryChange(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry değişikliğini bildir"""
        # Debounce için pending updates'e ekle
        node_id = id(node)
        self._pending_updates[node_id] = {
            'type': 'geometry',
            'node': node,
            'old_size': old_size,
            'new_size': new_size
        }
        self._update_timer.start(50)  # 50ms debounce
    
    def notifyContentSizeChange(self, node: 'Node', content_size: tuple):
        """Content boyut değişikliğini bildir"""
        # Debounce için pending updates'e ekle
        node_id = id(node)
        self._pending_updates[node_id] = {
            'type': 'content',
            'node': node,
            'content_size': content_size
        }
        self._update_timer.start(50)  # 50ms debounce
    
    def _process_pending_updates(self):
        """Bekleyen güncellemeleri işle"""
        for update in self._pending_updates.values():
            if update['type'] == 'geometry':
                self.geometryChanged.emit(update['node'], update['old_size'], update['new_size'])
            elif update['type'] == 'content':
                self.contentSizeChanged.emit(update['node'], update['content_size'])
        
        self._pending_updates.clear()
    
    def _notify_geometry_observers(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry observer'larını bilgilendir"""
        for observer in self._observers:
            try:
                observer.onGeometryChanged(node, old_size, new_size)
            except Exception as e:
                print(f"Error in geometry observer: {e}")
    
    def _notify_content_observers(self, node: 'Node', content_size: tuple):
        """Content observer'larını bilgilendir"""
        for observer in self._observers:
            try:
                observer.onContentSizeChanged(node, content_size)
            except Exception as e:
                print(f"Error in content observer: {e}")


class AutoResizeObserver(GeometryObserver):
    """Otomatik boyutlandırma için observer"""
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.min_width = 150
        self.min_height = 80
        self.max_width = 500
        self.max_height = 800
        self.padding = 10
    
    def onGeometryChanged(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry değiştiğinde socket pozisyonlarını güncelle"""
        if self.enabled and hasattr(node, 'updateConnectedEdges'):
            node.updateConnectedEdges()
    
    def onContentSizeChanged(self, node: 'Node', content_size: tuple):
        """Content boyutu değiştiğinde node boyutunu güncelle"""
        if not self.enabled or not hasattr(node, 'grNode') or not node.grNode:
            return
        
        grNode = node.grNode
        
        # Yeni boyutu hesapla
        content_width, content_height = content_size
        new_width = content_width + 2 * self.padding
        new_height = content_height + grNode.title_height + 2 * self.padding
        
        # Min/max sınırları uygula
        new_width = max(self.min_width, min(new_width, self.max_width))
        new_height = max(self.min_height, min(new_height, self.max_height))
        
        # Boyut değişti mi kontrol et
        if abs(new_width - grNode.width) > 1 or abs(new_height - grNode.height) > 1:
            old_size = (grNode.width, grNode.height)
            
            # Geometry değişikliğini hazırla
            grNode.prepareGeometryChange()
            
            # Yeni boyutu ayarla
            grNode.width = new_width
            grNode.height = new_height
            
            # Content geometrisini güncelle
            if hasattr(grNode, 'updateContentGeometry'):
                grNode.updateContentGeometry()
            
            # Observer'lara bildir
            if hasattr(node, 'geometry_manager'):
                node.geometry_manager.notifyGeometryChange(node, old_size, (new_width, new_height))
            
            # Scene'i güncelle
            grNode.update()
    
    def setEnabled(self, enabled: bool):
        """Auto-resize'ı aç/kapat"""
        self.enabled = enabled
    
    def setConstraints(self, min_width: int, min_height: int, max_width: int, max_height: int):
        """Boyut sınırlarını ayarla"""
        self.min_width = min_width
        self.min_height = min_height
        self.max_width = max_width
        self.max_height = max_height
    
    def setPadding(self, padding: int):
        """Padding'i ayarla"""
        self.padding = padding


class SocketPositionObserver(GeometryObserver):
    """Socket pozisyonlarını güncelleyen observer"""
    
    def onGeometryChanged(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry değiştiğinde socket pozisyonlarını güncelle"""
        if hasattr(node, 'inputs') and hasattr(node, 'outputs'):
            # Tüm socket'ların pozisyonlarını güncelle
            for socket in node.inputs + node.outputs:
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()
            
            # Bağlı edge'leri güncelle
            if hasattr(node, 'updateConnectedEdges'):
                node.updateConnectedEdges()
    
    def onContentSizeChanged(self, node: 'Node', content_size: tuple):
        """Content boyutu değiştiğinde - geometry değişikliği bekle"""
        pass


class SceneUpdateObserver(GeometryObserver):
    """Scene güncellemelerini yöneten observer"""
    
    def onGeometryChanged(self, node: 'Node', old_size: tuple, new_size: tuple):
        """Geometry değiştiğinde scene'i güncelle"""
        if hasattr(node, 'grNode') and node.grNode:
            # Scene'deki diğer node'ları etkileyebilecek değişiklikler için
            if hasattr(node.grNode, 'scene') and node.grNode.scene():
                node.grNode.scene().update()
    
    def onContentSizeChanged(self, node: 'Node', content_size: tuple):
        """Content boyutu değiştiğinde - geometry değişikliği bekle"""
        pass


# Global geometry manager instance
_geometry_manager = None

def getGeometryManager() -> NodeGeometryManager:
    """Global geometry manager'ı al"""
    global _geometry_manager
    if _geometry_manager is None:
        _geometry_manager = NodeGeometryManager()
        
        # Default observer'ları ekle
        _geometry_manager.addObserver(AutoResizeObserver())
        _geometry_manager.addObserver(SocketPositionObserver())
        _geometry_manager.addObserver(SceneUpdateObserver())
    
    return _geometry_manager
