from json.decoder import <PERSON><PERSON><PERSON><PERSON>odeError
from subprocess import call
from typing import Dict
from PyQt5 import <PERSON>t<PERSON><PERSON>
from PyQt5 import QtGui
from PyQt5 import QtWidgets
from qtpy.QtCore import Qt
from apps.execution_node_editor.ui.PyQtJsonModel import QJsonModel

from PyQt5.QtWidgets import QAbstractItemView, QTreeView

import json 

def countLines(text):
    return len(text.split('\n'))

class JsonEditor(QtWidgets.QDockWidget):
    def __init__(self):
        super(JsonEditor, self).__init__()
        self.setFeatures(QtWidgets.QDockWidget.DockWidgetMovable)
        self.tree = QTreeView(self)
        self.setWidget(self.tree)
        self.setMinimumWidth(100)
        self.setMinimumHeight(100)
        self.setWindowTitle("NODE SETTINGS")
        self.setFont(QtGui.QFont('Roboto', 10))
        self.callback = None
        self.tree.setEditTriggers(QAbstractItemView.AllEditTriggers)

    def update(self, json_dict, callback, active):
        if active:
            self.setWidget(self.tree)
            self.json_model = QJsonModel(json_data=json_dict)
            self.json_model.dataChanged.connect(self.apply_change)
            self.tree.setModel(self.json_model)
            self.callback = callback
        else:
            from PyQt5.QtWidgets import QWidget
            empty = QWidget()
            empty.setStyleSheet("background: #232323;")
            self.setWidget(empty)
            self.tree.setModel(None)
            self.callback = None

    def apply_change(self):
        had_error = True
        json_dict = {}
        try:
            json_dict = self.json_model.as_dict
            had_error = False
        except:
            had_error = True

        if had_error == False and self.callback is not None:
            self.callback(json_dict)
