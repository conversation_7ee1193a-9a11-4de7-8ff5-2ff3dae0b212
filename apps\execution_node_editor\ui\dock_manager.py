from PyQt5.QtWidgets import <PERSON>D<PERSON><PERSON>idget, <PERSON><PERSON><PERSON><PERSON>, QHBoxLayout, QWidget
from PyQt5.QtGui import QPixmap, QPainter
from PyQt5.QtCore import Qt
from apps.execution_node_editor.localization import i18n
from apps.execution_node_editor.ui.json_editor import <PERSON><PERSON><PERSON><PERSON>or
from apps.execution_node_editor.ui.drag_listbox import QDMDragListbox

class NodesDockManager:
    def __init__(self, window):
        self.window = window
        self.createNodesDock()
        i18n.languageChanged.connect(self.updateTexts)

    def createNodesDock(self):
        # Debug kodu kaldırıldı
        self.window.nodesListWidget = QDMDragListbox()
        self.window.nodesListWidget.setStyleSheet("background-color: #232323; border: none;")
        nodes_header = QWidget()
        nodes_header_layout = QHBoxLayout()
        nodes_header_layout.setContentsMargins(8, 6, 8, 6)
        nodes_header_layout.setSpacing(0)
        icon_path = 'apps/execution_node_editor/assets/icons/Editor/write_to_layer.png'
        icon_pixmap = QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        white_icon = QPixmap(icon_pixmap.size())
        white_icon.fill(Qt.transparent)
        painter = QPainter(white_icon)
        painter.setCompositionMode(QPainter.CompositionMode_Source)
        painter.drawPixmap(0, 0, icon_pixmap)
        painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
        painter.fillRect(white_icon.rect(), Qt.white)
        painter.end()
        icon_label = QLabel()
        icon_label.setPixmap(white_icon)
        icon_label.setContentsMargins(0, 0, 6, 0)
        self.nodes_title_label = QLabel(i18n.get("nodes_dock_title"))
        self.nodes_title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.nodes_title_label.setStyleSheet("background: #313131; color: #cccccc; padding: 2px 16px; border-radius: 6px; font-size: 13px; font-weight: normal;")
        self.nodes_title_label.setContentsMargins(0, 0, 0, 0)
        self.nodes_title_label.setMinimumHeight(22)
        nodes_header_layout.addWidget(icon_label)
        nodes_header_layout.addWidget(self.nodes_title_label)
        nodes_header_layout.addStretch()
        nodes_header_layout.setAlignment(Qt.AlignLeft)
        nodes_header.setLayout(nodes_header_layout)
        nodes_header.setStyleSheet("background: #232323;")
        self.window.nodesDock = QDockWidget()
        self.window.nodesDock.setTitleBarWidget(nodes_header)
        self.window.nodesDock.setFont(self.window.font())
        self.window.nodesDock.setFeatures(QDockWidget.DockWidgetMovable)
        self.window.nodesDock.setWidget(self.window.nodesListWidget)
        self.window.nodesDock.setFloating(False)
        self.window.nodesDock.setStyleSheet("background-color: #232323; border: none;")
        self.window.nodesDock.setMinimumWidth(1)
        self.window.nodesDock.setMaximumWidth(10000)
        self.window.addDockWidget(Qt.RightDockWidgetArea, self.window.nodesDock)

    def isVisible(self):
        return self.window.nodesDock.isVisible()

    def show(self):
        self.window.nodesDock.show()

    def hide(self):
        self.window.nodesDock.hide()

    def setWindowIcon(self, icon):
        self.window.nodesDock.setWindowIcon(icon)

    def updateTexts(self):
        self.nodes_title_label.setText(i18n.get("nodes_dock_title"))
        self.window.nodesDock.setWindowTitle(i18n.get("nodes_dock_title"))

class SettingsDockManager:
    def __init__(self, window):
        self.window = window
        self.createSettingsDock()
        i18n.languageChanged.connect(self.updateTexts)

    def createSettingsDock(self):
        settings_header = QWidget()
        settings_header_layout = QHBoxLayout()
        settings_header_layout.setContentsMargins(8, 6, 8, 6)
        settings_header_layout.setSpacing(0)
        icon_path = 'apps/execution_node_editor/assets/icons/Editor/settings.png'
        icon_pixmap = QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        white_icon = QPixmap(icon_pixmap.size())
        white_icon.fill(Qt.transparent)
        painter = QPainter(white_icon)
        painter.setCompositionMode(QPainter.CompositionMode_Source)
        painter.drawPixmap(0, 0, icon_pixmap)
        painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
        painter.fillRect(white_icon.rect(), Qt.white)
        painter.end()
        icon_label = QLabel()
        icon_label.setPixmap(white_icon)
        icon_label.setContentsMargins(0, 0, 6, 0)
        self.settings_title_label = QLabel(i18n.get("settings_dock_title"))
        self.settings_title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.settings_title_label.setStyleSheet("background: #313131; color: #cccccc; padding: 2px 16px; border-radius: 6px; font-size: 13px; font-weight: normal;")
        self.settings_title_label.setContentsMargins(0, 0, 0, 0)
        self.settings_title_label.setMinimumHeight(22)
        settings_header_layout.addWidget(icon_label)
        settings_header_layout.addWidget(self.settings_title_label)
        settings_header_layout.addStretch()
        settings_header_layout.setAlignment(Qt.AlignLeft)
        settings_header.setLayout(settings_header_layout)
        settings_header.setStyleSheet("background: #232323;")
        self.window.settingsDock = JsonEditor()
        self.window.settingsDock.setTitleBarWidget(settings_header)
        self.window.settingsDock.setStyleSheet("background-color: #232323; border: none;")
        self.window.settingsDock.setMinimumWidth(1)
        self.window.settingsDock.setMaximumWidth(10000)
        if hasattr(self.window.settingsDock, 'tree'):
            self.window.settingsDock.tree.setStyleSheet("background-color: #232323; color: #cccccc; border: none;")
        self.window.addDockWidget(Qt.RightDockWidgetArea, self.window.settingsDock)

    def isVisible(self):
        return self.window.settingsDock.isVisible()

    def show(self):
        self.window.settingsDock.show()

    def hide(self):
        self.window.settingsDock.hide()

    def setWindowIcon(self, icon):
        self.window.settingsDock.setWindowIcon(icon)

    def update(self, *args, **kwargs):
        if hasattr(self.window.settingsDock, 'update'):
            self.window.settingsDock.update(*args, **kwargs)

    def updateTexts(self):
        self.settings_title_label.setText(i18n.get("settings_dock_title"))
        self.window.settingsDock.setWindowTitle(i18n.get("settings_dock_title")) 