# -*- coding: utf-8 -*-
"""
Node Layout Manager - Property'leri düzenlemek için layout yönetim sistemi
"""
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from enum import Enum
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout, QGroupBox, QScrollArea, QFrame
from qtpy.QtCore import Qt, Signal, QMargins
from qtpy.QtGui import QFont

from nodeeditor.node_property_system import PropertyWidget, PropertyWidgetFactory, PropertyValue

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class LayoutType(Enum):
    """Layout türleri"""
    VERTICAL = "vertical"
    HORIZONTAL = "horizontal"
    GRID = "grid"
    FORM = "form"
    GROUPED = "grouped"


class LayoutConstraints:
    """Layout kısıtlamaları"""
    
    def __init__(self):
        self.min_width = 150
        self.min_height = 80
        self.max_width = 500
        self.max_height = 800
        self.padding = QMargins(8, 8, 8, 8)
        self.spacing = 4
        self.item_spacing = 2
        self.group_spacing = 8
        self.scroll_enabled = True
        self.auto_resize = True


class PropertyGroup:
    """Property grubu"""
    
    def __init__(self, name: str, title: str = None, collapsible: bool = False):
        self.name = name
        self.title = title or name.replace('_', ' ').title()
        self.collapsible = collapsible
        self.collapsed = False
        self.properties: List[str] = []
        self.widget: Optional[QGroupBox] = None
    
    def addProperty(self, property_name: str):
        """Property ekle"""
        if property_name not in self.properties:
            self.properties.append(property_name)
    
    def removeProperty(self, property_name: str):
        """Property kaldır"""
        if property_name in self.properties:
            self.properties.remove(property_name)


class NodeLayoutManager:
    """Node layout yöneticisi"""
    
    def __init__(self, node: 'Node'):
        self.node = node
        self.constraints = LayoutConstraints()
        self.layout_type = LayoutType.VERTICAL
        self.groups: Dict[str, PropertyGroup] = {}
        self.property_order: List[str] = []
        self.property_widgets: Dict[str, PropertyWidget] = {}
        self.main_widget: Optional[QWidget] = None
        self.main_layout = None
        
        # Default group
        self.addGroup("default", "Properties")
    
    def setLayoutType(self, layout_type: LayoutType):
        """Layout türünü ayarla"""
        self.layout_type = layout_type
        self.rebuildLayout()
    
    def setConstraints(self, constraints: LayoutConstraints):
        """Layout kısıtlamalarını ayarla"""
        self.constraints = constraints
        self.applyConstraints()
    
    def addGroup(self, name: str, title: str = None, collapsible: bool = False) -> PropertyGroup:
        """Property grubu ekle"""
        group = PropertyGroup(name, title, collapsible)
        self.groups[name] = group
        return group
    
    def removeGroup(self, name: str):
        """Property grubunu kaldır"""
        if name in self.groups and name != "default":
            # Group'taki property'leri default group'a taşı
            group = self.groups[name]
            default_group = self.groups.get("default")
            if default_group:
                for prop_name in group.properties:
                    default_group.addProperty(prop_name)
            
            del self.groups[name]
            self.rebuildLayout()
    
    def addPropertyToGroup(self, property_name: str, group_name: str = "default"):
        """Property'yi gruba ekle"""
        if group_name in self.groups:
            # Önce diğer gruplardan kaldır
            for group in self.groups.values():
                group.removeProperty(property_name)
            
            # Belirtilen gruba ekle
            self.groups[group_name].addProperty(property_name)
            
            # Property order'ı güncelle
            if property_name not in self.property_order:
                self.property_order.append(property_name)
            
            self.rebuildLayout()
    
    def setPropertyOrder(self, property_names: List[str]):
        """Property sırasını ayarla"""
        self.property_order = property_names.copy()
        self.rebuildLayout()
    
    def createMainWidget(self) -> QWidget:
        """Ana widget'ı oluştur"""
        if self.main_widget:
            self.main_widget.setParent(None)
            self.main_widget.deleteLater()
        
        self.main_widget = QWidget()
        self.main_widget.setObjectName("NodeLayoutMainWidget")
        
        # Ana layout oluştur
        if self.layout_type == LayoutType.VERTICAL:
            self.main_layout = QVBoxLayout()
        elif self.layout_type == LayoutType.HORIZONTAL:
            self.main_layout = QHBoxLayout()
        elif self.layout_type == LayoutType.GRID:
            self.main_layout = QGridLayout()
        elif self.layout_type == LayoutType.FORM:
            self.main_layout = QFormLayout()
        else:
            self.main_layout = QVBoxLayout()
        
        # Layout ayarları
        self.main_layout.setContentsMargins(self.constraints.padding)
        self.main_layout.setSpacing(self.constraints.spacing)
        
        # Scroll area ekle (gerekirse)
        if self.constraints.scroll_enabled:
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            scroll_area.setFrameStyle(QFrame.NoFrame)
            
            content_widget = QWidget()
            content_widget.setLayout(self.main_layout)
            scroll_area.setWidget(content_widget)
            
            # Scroll area için wrapper layout
            wrapper_layout = QVBoxLayout()
            wrapper_layout.setContentsMargins(0, 0, 0, 0)
            wrapper_layout.addWidget(scroll_area)
            self.main_widget.setLayout(wrapper_layout)
        else:
            self.main_widget.setLayout(self.main_layout)
        
        # Property widget'larını oluştur
        self.createPropertyWidgets()
        
        # Kısıtlamaları uygula
        self.applyConstraints()
        
        return self.main_widget
    
    def createPropertyWidgets(self):
        """Property widget'larını oluştur"""
        if not hasattr(self.node, 'property_manager'):
            return
        
        # Mevcut widget'ları temizle
        self.clearPropertyWidgets()
        
        # Grupları sırala ve widget'ları oluştur
        for group_name, group in self.groups.items():
            if not group.properties:
                continue
            
            # Group widget oluştur
            if len(self.groups) > 1:  # Birden fazla grup varsa group box kullan
                group_widget = QGroupBox(group.title)
                group_widget.setObjectName(f"PropertyGroup_{group_name}")
                group_layout = self.createGroupLayout()
                group_widget.setLayout(group_layout)
                group.widget = group_widget
            else:
                group_widget = None
                group_layout = self.main_layout
            
            # Group'taki property'leri ekle
            sorted_properties = self.getSortedProperties(group.properties)
            
            for prop_name in sorted_properties:
                prop_value = self.node.property_manager._properties.get(prop_name)
                if prop_value and prop_value.definition.visible:
                    try:
                        widget = PropertyWidgetFactory.create_widget(prop_value)
                        widget.valueChanged.connect(
                            lambda value, name=prop_name: self.onPropertyWidgetChanged(name, value)
                        )
                        
                        self.property_widgets[prop_name] = widget
                        
                        # Layout'a ekle
                        if self.layout_type == LayoutType.GRID:
                            row = len(group_layout.children()) // 2
                            group_layout.addWidget(widget, row, 0, 1, 2)
                        elif self.layout_type == LayoutType.FORM:
                            group_layout.addRow(widget)
                        else:
                            group_layout.addWidget(widget)
                    
                    except Exception as e:
                        print(f"Error creating widget for property {prop_name}: {e}")
            
            # Group widget'ı ana layout'a ekle
            if group_widget:
                if self.layout_type == LayoutType.GRID:
                    row = len(self.main_layout.children())
                    self.main_layout.addWidget(group_widget, row, 0, 1, 2)
                else:
                    self.main_layout.addWidget(group_widget)
        
        # Stretch ekle (vertical layout için)
        if self.layout_type == LayoutType.VERTICAL:
            self.main_layout.addStretch()
    
    def createGroupLayout(self):
        """Group için layout oluştur"""
        if self.layout_type == LayoutType.HORIZONTAL:
            return QHBoxLayout()
        elif self.layout_type == LayoutType.GRID:
            return QGridLayout()
        elif self.layout_type == LayoutType.FORM:
            return QFormLayout()
        else:
            return QVBoxLayout()
    
    def getSortedProperties(self, property_names: List[str]) -> List[str]:
        """Property'leri sırala"""
        # Önce property_order'daki sırayı kullan
        ordered = []
        unordered = []
        
        for prop_name in property_names:
            if prop_name in self.property_order:
                ordered.append((self.property_order.index(prop_name), prop_name))
            else:
                unordered.append(prop_name)
        
        # Sıralı olanları sıraya koy
        ordered.sort(key=lambda x: x[0])
        result = [prop_name for _, prop_name in ordered]
        
        # Sırasız olanları alfabetik olarak ekle
        result.extend(sorted(unordered))
        
        return result
    
    def clearPropertyWidgets(self):
        """Property widget'larını temizle"""
        for widget in self.property_widgets.values():
            widget.setParent(None)
            widget.deleteLater()
        self.property_widgets.clear()
        
        # Group widget'larını temizle
        for group in self.groups.values():
            if group.widget:
                group.widget.setParent(None)
                group.widget.deleteLater()
                group.widget = None
    
    def rebuildLayout(self):
        """Layout'u yeniden oluştur"""
        if self.main_widget:
            self.createPropertyWidgets()
    
    def applyConstraints(self):
        """Kısıtlamaları uygula"""
        if not self.main_widget:
            return
        
        # Boyut kısıtlamaları
        self.main_widget.setMinimumSize(self.constraints.min_width, self.constraints.min_height)
        self.main_widget.setMaximumSize(self.constraints.max_width, self.constraints.max_height)
        
        # Layout spacing'leri güncelle
        if self.main_layout:
            self.main_layout.setContentsMargins(self.constraints.padding)
            self.main_layout.setSpacing(self.constraints.spacing)
    
    def onPropertyWidgetChanged(self, property_name: str, value: Any):
        """Property widget değiştiğinde"""
        # Auto-resize varsa boyutu güncelle
        if self.constraints.auto_resize and self.main_widget:
            self.main_widget.updateGeometry()
    
    def getMainWidget(self) -> Optional[QWidget]:
        """Ana widget'ı al"""
        return self.main_widget
    
    def getPropertyWidget(self, property_name: str) -> Optional[PropertyWidget]:
        """Belirli bir property'nin widget'ını al"""
        return self.property_widgets.get(property_name)
