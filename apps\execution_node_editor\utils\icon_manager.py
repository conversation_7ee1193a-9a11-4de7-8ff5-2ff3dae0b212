# -*- coding: utf-8 -*-
"""
Icon Manager - İkonları internetten indirip cache'leyerek yönetir
"""
import os
import requests
import hashlib
from typing import Optional
from qtpy.QtGui import QPixmap, QIcon
from qtpy.QtCore import QSize

class IconManager:
    """İkon yönetim sistemi"""
    
    def __init__(self, cache_dir: str = None):
        """
        :param cache_dir: İkonların cache'leneceği dizin
        """
        if cache_dir is None:
            # Varsayılan cache dizini
            cache_dir = os.path.join(
                os.path.dirname(os.path.dirname(__file__)), 
                'assets', 'icons', 'cached'
            )
        
        self.cache_dir = cache_dir
        self.ensure_cache_dir()
        
        # Ücretsiz ikon siteleri
        self.icon_sources = {
            'feather': 'https://raw.githubusercontent.com/feathericons/feather/master/icons/{}.svg',
            'heroicons': 'https://raw.githubusercontent.com/tailwindlabs/heroicons/master/src/24/outline/{}.svg',
            'lucide': 'https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/{}.svg',
            'tabler': 'https://raw.githubusercontent.com/tabler/tabler-icons/master/icons/{}.svg'
        }
    
    def ensure_cache_dir(self):
        """Cache dizinini oluştur"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)
    
    def get_cache_path(self, icon_name: str, source: str = 'feather') -> str:
        """Cache dosya yolunu al"""
        # URL'den hash oluştur
        url = self.icon_sources[source].format(icon_name)
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        filename = f"{source}_{icon_name}_{url_hash}.svg"
        return os.path.join(self.cache_dir, filename)
    
    def download_icon(self, icon_name: str, source: str = 'feather') -> bool:
        """İkonu indir ve cache'le"""
        try:
            url = self.icon_sources[source].format(icon_name)
            cache_path = self.get_cache_path(icon_name, source)
            
            # Zaten cache'de varsa indirme
            if os.path.exists(cache_path):
                return True
            
            print(f"İkon indiriliyor: {icon_name} from {source}")
            
            # İkonu indir
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # Cache'e kaydet
            with open(cache_path, 'wb') as f:
                f.write(response.content)
            
            print(f"İkon cache'lendi: {cache_path}")
            return True
            
        except Exception as e:
            print(f"İkon indirme hatası ({icon_name}): {e}")
            return False
    
    def get_icon(self, icon_name: str, source: str = 'feather', size: QSize = None) -> Optional[QIcon]:
        """İkonu al (cache'den veya indirerek)"""
        cache_path = self.get_cache_path(icon_name, source)
        
        # Cache'de yoksa indir
        if not os.path.exists(cache_path):
            if not self.download_icon(icon_name, source):
                return None
        
        # QIcon oluştur
        try:
            pixmap = QPixmap(cache_path)
            if pixmap.isNull():
                return None
            
            if size:
                pixmap = pixmap.scaled(size, aspectRatioMode=1, transformMode=1)
            
            return QIcon(pixmap)
        except Exception as e:
            print(f"İkon yükleme hatası ({icon_name}): {e}")
            return None
    
    def get_pixmap(self, icon_name: str, source: str = 'feather', size: QSize = None) -> Optional[QPixmap]:
        """Pixmap al (cache'den veya indirerek)"""
        cache_path = self.get_cache_path(icon_name, source)
        
        # Cache'de yoksa indir
        if not os.path.exists(cache_path):
            if not self.download_icon(icon_name, source):
                return None
        
        # QPixmap oluştur
        try:
            pixmap = QPixmap(cache_path)
            if pixmap.isNull():
                return None
            
            if size:
                pixmap = pixmap.scaled(size, aspectRatioMode=1, transformMode=1)
            
            return pixmap
        except Exception as e:
            print(f"Pixmap yükleme hatası ({icon_name}): {e}")
            return None
    
    def preload_icons(self, icon_list: list, source: str = 'feather'):
        """İkonları önceden yükle"""
        print(f"İkonlar önceden yükleniyor: {len(icon_list)} adet")
        
        for icon_name in icon_list:
            self.download_icon(icon_name, source)
        
        print("İkon ön yükleme tamamlandı")
    
    def clear_cache(self):
        """Cache'i temizle"""
        try:
            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            print("İkon cache'i temizlendi")
        except Exception as e:
            print(f"Cache temizleme hatası: {e}")
    
    def get_cache_info(self) -> dict:
        """Cache bilgilerini al"""
        try:
            files = os.listdir(self.cache_dir)
            total_size = sum(
                os.path.getsize(os.path.join(self.cache_dir, f)) 
                for f in files if os.path.isfile(os.path.join(self.cache_dir, f))
            )
            
            return {
                'file_count': len(files),
                'total_size_mb': total_size / (1024 * 1024),
                'cache_dir': self.cache_dir
            }
        except Exception as e:
            print(f"Cache bilgi alma hatası: {e}")
            return {'file_count': 0, 'total_size_mb': 0, 'cache_dir': self.cache_dir}


# Global icon manager instance
_icon_manager = None

def get_icon_manager() -> IconManager:
    """Global icon manager'ı al"""
    global _icon_manager
    if _icon_manager is None:
        _icon_manager = IconManager()
    return _icon_manager

def get_icon(icon_name: str, source: str = 'feather', size: QSize = None) -> Optional[QIcon]:
    """Kolay kullanım için global fonksiyon"""
    return get_icon_manager().get_icon(icon_name, source, size)

def get_pixmap(icon_name: str, source: str = 'feather', size: QSize = None) -> Optional[QPixmap]:
    """Kolay kullanım için global fonksiyon"""
    return get_icon_manager().get_pixmap(icon_name, source, size)

def preload_common_icons():
    """Yaygın kullanılan ikonları önceden yükle"""
    common_icons = [
        'image', 'video', 'folder', 'file', 'download', 'upload',
        'play', 'pause', 'stop', 'settings', 'trash-2', 'x',
        'check', 'plus', 'minus', 'edit', 'save', 'search',
        'filter', 'refresh', 'home', 'user', 'help-circle'
    ]
    
    manager = get_icon_manager()
    manager.preload_icons(common_icons, 'feather')
