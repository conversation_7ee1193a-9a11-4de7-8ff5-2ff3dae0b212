/* === GLOBAL === */
* {
    font-family: 'Segoe UI', 'Roboto', 'Open Sans', Arial, sans-serif;
    font-size: 13px;
    color: #cccccc;
}

QMainWindow, QDialog, QFrame, QToolBar {
    background: #232323;
    border: none;
}

/* === PANEL BAŞLIKLARI (Unity tarzı) === */
QDockWidget::title {
    background: #232323 !important;
    color: #cccccc;
    font-weight: bold;
    font-size: 13px;
    padding: 7px 16px 7px 16px;
    min-height: 28px;
    max-height: 32px;
    border-bottom: 1px solid #2d2d2d;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    letter-spacing: 0.7px;
}

/* === DOCK & KART === */
QDockWidget {
    background: #232323 !important;
    border-radius: 7px;
    border: 1px solid #2d2d2d;
}
QDockWidget QWidget {
    background: #232323 !important;
}

/* === SEKMELER === */
QTabBar::tab {
    background: #232323;
    color: #888888;
    border-radius: 6px 6px 0 0;
    padding: 8px 18px;
    margin-right: 2px;
    font-weight: normal;
    font-size: 13px;
    border: 1px solid #2d2d2d;
    border-bottom: none;
}
QTabBar::tab:selected {
    background: #2a2a2a;
    color: #cccccc;
    font-weight: normal;
}
QTabBar::tab:hover {
    background: #282828;
    color: #cccccc;
}

/* === BUTONLAR === */
QPushButton {
    background: #282828;
    color: #cccccc;
    border-radius: 6px;
    padding: 7px 16px;
    border: 1px solid #2d2d2d;
    font-size: 13px;
    font-weight: 500;
}
QPushButton:hover {
    background: #3a3a3a;
    color: #ffffff;
}
QPushButton:pressed {
    background: #232323;
}

/* === LİSTELER & KARTLAR === */
QTreeView, QListView {
    background: transparent;
    border: none;
    padding: 6px;
}
QTreeView {
    background: #232323 !important;
    border: none;
}
QTreeView::item, QListView::item {
    background: #262626;
    border-radius: 6px;
    margin: 3px 0;
    padding: 7px 14px;
    border: 1px solid #2d2d2d;
    font-size: 13px;
}
QTreeView::item:selected, QListView::item:selected {
    background: #3a3a3a;
    color: #ffffff;
    border: 1.5px solid #3a3a3a;
}
QTreeView::item:hover, QListView::item:hover {
    background: #282828;
    color: #ffffff;
    border: 1.5px solid #282828;
}

/* === GİRİŞ KUTULARI === */
QLineEdit, QTextEdit {
    color: #cccccc;
    background: #232323;
    border: 1px solid #2d2d2d;
    border-radius: 5px;
    padding: 6px 10px;
    font-size: 13px;
}
QLineEdit:focus, QTextEdit:focus {
    border: 1.5px solid #3a3a3a;
    background: #262626;
}

/* === STATUS BAR === */
QStatusBar {
    background: #232323;
    color: #b0b0b0;
    border-top: 1px solid #2d2d2d;
    font-size: 12px;
    padding-left: 8px;
}

/* === PANEL BAŞLIK ETİKETİ === */
.unity-panel-header {
    background: #232323;
    color: #cccccc;
    font-weight: bold;
    padding: 7px 16px;
    border-bottom: 1px solid #2d2d2d;
    font-size: 13px;
    letter-spacing: 0.7px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

/* === SPLITTER HANDLE === */
QSplitter::handle {
    background: #2d2d2d;
    width: 1px;
    height: 1px;
    min-width: 1px;
    min-height: 1px;
    margin: 0;
    border: none;
    border-radius: 0;
}

QMainWindow::separator {
    background: #2d2d2d;
    width: 2px;
    height: 2px;
    border: none;
}

/* Node başlığı için */
.QDMNodeTitle {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0.5px;
    color: #fff;
}

/* Port isimleri için */
.QDMNodeSocketLabel {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    font-size: 13px;
    font-weight: 500;
    color: #e0e0e0;
}

QToolTip {
    background-color: #222;
    color: #fff;
    border: 1px solid #555;
    font-size: 13px;
}
