# -*- coding: utf-8 -*-
"""
A module containing the representation of the NodeEditor's Scene
"""
from collections import OrderedDict
from nodeeditor.utils import dumpException
from nodeeditor.node_serializable import Serializable
from nodeeditor.node_graphics_scene import QDMGraphicsScene
from nodeeditor.node_node import Node
from nodeeditor.node_edge import Edge
from nodeeditor.node_scene_history import SceneHistory
from nodeeditor.node_scene_clipboard import SceneClipboard
from nodeeditor.scene_selection_manager import SceneSelectionManager
from nodeeditor.scene_file_manager import SceneFileManager
from nodeeditor.scene_node_manager import SceneNodeManager
from PyQt5.QtWidgets import QGraphicsView
from PyQt5.QtCore import QPointF
from typing import Callable


DEBUG_REMOVE_WARNINGS = False


class InvalidFile(Exception):
    pass


class Scene(Serializable):
    """Class representing NodeEditor's `Scene`"""

    def __init__(self):
        """
        :Instance Attributes:

            - **nodes** - list of `Nodes` in this `Scene`
            - **edges** - list of `Edges` in this `Scene`
            - **history** - Instance of :class:`~nodeeditor.node_scene_history.SceneHistory`
            - **clipboard** - Instance of :class:`~nodeeditor.node_scene_clipboard.SceneClipboard`
            - **scene_width** - width of this `Scene` in pixels
            - **scene_height** - height of this `Scene` in pixels
        """
        super().__init__()
        self.nodes = []
        self.edges = []

        # current filename assigned to this scene
        self.filename = None

        self.scene_width = 64000
        self.scene_height = 64000

        self._has_been_modified = False
        self._has_been_modified_listeners = []

        # Manager'ları başlat
        self.initUI()
        self.history = SceneHistory(self)
        self.clipboard = SceneClipboard(self)
        self.selection_manager = SceneSelectionManager(self)
        self.file_manager = SceneFileManager(self)
        self.node_manager = SceneNodeManager(self)

        # Manager sinyallerini bağla
        self._connect_manager_signals()

    def _connect_manager_signals(self):
        """Manager sinyallerini bağlar"""
        # Selection manager sinyalleri
        self.selection_manager.item_selected.connect(self._on_item_selected)
        self.selection_manager.items_deselected.connect(self._on_items_deselected)

        # Node manager sinyalleri
        self.node_manager.node_added.connect(self._on_node_added)
        self.node_manager.node_removed.connect(self._on_node_removed)
        self.node_manager.edge_added.connect(self._on_edge_added)
        self.node_manager.edge_removed.connect(self._on_edge_removed)

    def _on_item_selected(self):
        """Selection manager'dan gelen item selected sinyali"""
        pass  # Gerekirse ek işlemler yapılabilir

    def _on_items_deselected(self):
        """Selection manager'dan gelen items deselected sinyali"""
        pass  # Gerekirse ek işlemler yapılabilir

    def _on_node_added(self, node):
        """Node eklendiğinde çağrılır"""
        pass  # Gerekirse ek işlemler yapılabilir

    def _on_node_removed(self, node):
        """Node kaldırıldığında çağrılır"""
        pass  # Gerekirse ek işlemler yapılabilir

    def _on_edge_added(self, edge):
        """Edge eklendiğinde çağrılır"""
        pass  # Gerekirse ek işlemler yapılabilir

    def _on_edge_removed(self, edge):
        """Edge kaldırıldığında çağrılır"""
        pass  # Gerekirse ek işlemler yapılabilir

    @property
    def has_been_modified(self):
        """
        Has this `Scene` been modified?

        :getter: ``True`` if the `Scene` has been modified
        :setter: set new state. Triggers `Has Been Modified` event
        :type: ``bool``
        """
        return self._has_been_modified

    @has_been_modified.setter
    def has_been_modified(self, value):
        if not self._has_been_modified and value:
            # set it now, because we will be reading it soon
            self._has_been_modified = value

            # call all registered listeners
            for callback in self._has_been_modified_listeners:
                callback()

        self._has_been_modified = value

    def initUI(self):
        """Set up Graphics Scene Instance"""
        self.grScene = QDMGraphicsScene(self)
        self.grScene.setGrScene(self.scene_width, self.scene_height)

    def getNodeByID(self, node_id: int):
        """Manager'a yönlendir"""
        return self.node_manager.get_node_by_id(node_id)

    def setSilentSelectionEvents(self, value: bool = True):
        """Manager'a yönlendir"""
        self.selection_manager.set_silent_selection_events(value)

    def onItemSelected(self, silent: bool = False):
        """Manager'a yönlendir"""
        self.selection_manager.on_item_selected(silent)

    def onItemsDeselected(self, silent: bool = False):
        """Manager'a yönlendir"""
        self.selection_manager.on_items_deselected(silent)

    def isModified(self) -> bool:
        """Is this `Scene` dirty aka `has been modified` ?

        :return: ``True`` if `Scene` has been modified
        :rtype: ``bool``
        """
        return self.has_been_modified

    def getSelectedItems(self) -> list:
        """Manager'a yönlendir"""
        return self.selection_manager.get_selected_items()

    def doDeselectItems(self, silent: bool = False) -> None:
        """Manager'a yönlendir"""
        self.selection_manager.deselect_all_items(silent)

    # our helper listener functions
    def addHasBeenModifiedListener(self, callback: 'function'):
        """
        Register callback for `Has Been Modified` event

        :param callback: callback function
        """
        self._has_been_modified_listeners.append(callback)

    def addItemSelectedListener(self, callback: 'function'):
        """Manager'a yönlendir"""
        self.selection_manager.add_item_selected_listener(callback)

    def addItemsDeselectedListener(self, callback: 'function'):
        """Manager'a yönlendir"""
        self.selection_manager.add_items_deselected_listener(callback)

    def addDragEnterListener(self, callback: 'function'):
        """
        Register callback for `Drag Enter` event

        :param callback: callback function
        """
        self.getView().addDragEnterListener(callback)

    def addDropListener(self, callback: 'function'):
        """
        Register callback for `Drop` event

        :param callback: callback function
        """
        self.getView().addDropListener(callback)

    # custom flag to detect node or edge has been selected....
    def resetLastSelectedStates(self):
        """Manager'a yönlendir"""
        self.selection_manager.reset_last_selected_states()

    def getView(self) -> 'QGraphicsView':
        """Shortcut for returning `Scene` ``QGraphicsView``

        :return: ``QGraphicsView`` attached to the `Scene`
        :rtype: ``QGraphicsView``
        """
        return self.grScene.views()[0]

    def getItemAt(self, pos: 'QPointF'):
        """Shortcut for retrieving item at provided `Scene` position

        :param pos: scene position
        :type pos: ``QPointF``
        :return: Qt Graphics Item at scene position
        :rtype: ``QGraphicsItem``
        """
        return self.getView().itemAt(pos)

    def addNode(self, node: Node):
        """Manager'a yönlendir"""
        self.node_manager.add_node(node)

    def addEdge(self, edge: Edge):
        """Manager'a yönlendir"""
        self.node_manager.add_edge(edge)

    def removeNode(self, node: Node):
        """Manager'a yönlendir"""
        self.node_manager.remove_node(node)

    def removeEdge(self, edge: Edge):
        """Manager'a yönlendir"""
        self.node_manager.remove_edge(edge)

    def clear(self):
        """Manager'a yönlendir"""
        self.node_manager.clear_scene()

    def saveGraphToFile(self, filename: str):
        """Manager'a yönlendir"""
        return self.file_manager.save_graph_to_file(filename)

    def saveSceneToFile(self, filename: str):
        """Manager'a yönlendir"""
        return self.file_manager.save_scene_to_file(filename)

    def loadFromFile(self, filename: str):
        """Manager'a yönlendir"""
        return self.file_manager.load_from_file(filename)

    def getEdgeClass(self):
        """Return the class representing Edge. Override me if needed"""
        return Edge

    def setNodeClassSelector(self, class_selecting_function: Callable) -> None:
        """Manager'a yönlendir"""
        self.node_manager.set_node_class_selector(class_selecting_function)

    def serialize(self) -> OrderedDict:
        nodes, edges = [], []
        for node in self.nodes:
            nodes.append(node.serialize())
        for edge in self.edges:
            edges.append(edge.serialize())
        return OrderedDict([
            ('id', self.id),
            ('scene_width', self.scene_width),
            ('scene_height', self.scene_height),
            ('nodes', nodes),
            ('edges', edges),
        ])

    def deserialize(self, data: dict, hashmap: dict = {}, restore_id: bool = True, *args, **kwargs) -> bool:
        hashmap = {}

        if restore_id:
            self.id = data['id']

        # -- deserialize NODES

        all_nodes = self.nodes.copy()

        # go through deserialized nodes:
        for node_data in data['nodes']:
            try:
                # Node class'ını manager'dan al
                node_class = self.node_manager.get_node_class(node_data)
                node_type = node_data['node_type']
                new_node = node_class(self, node_type)
                new_node.deserialize(
                    node_data, hashmap, restore_id, *args, **kwargs)
                new_node.onDeserialized(node_data)
                # print("New node for", node_data['title'])
            except:
                dumpException()

        # remove nodes which are left in the scene and were NOT in the serialized data!
        # that means they were not in the graph before...
        while all_nodes != []:
            node = all_nodes.pop()
            node.remove()

        # -- deserialize EDGES

        # Instead of recreating all the edges, reuse existing ones...
        # get list of all current edges:
        all_edges = self.edges.copy()

        # go through deserialized edges:
        for edge_data in data['edges']:
            # can we find this node in the scene?
            found = False
            for edge in all_edges:
                if edge.id == edge_data['id']:
                    found = edge
                    break

            if not found:
                new_edge = Edge(self).deserialize(
                    edge_data, hashmap, restore_id, *args, **kwargs)
                # print("New edge for", edge_data)
            else:
                found.deserialize(edge_data, hashmap,
                                  restore_id, *args, **kwargs)
                all_edges.remove(found)

        # remove nodes which are left in the scene and were NOT in the serialized data!
        # that means they were not in the graph before...
        while all_edges != []:
            edge = all_edges.pop()
            edge.remove()

        return True
