{"node_content_title": "Node Content", "node_content_text_placeholder": "Enter node content...", "custom_node_title": "Custom Node", "custom_node_content": "Custom Node Content", "source": "Source", "processing": "Processing", "output": "Output", "image_source_label": "Image", "image_source": "Image", "video_source_label": "Video", "video_source": "Video", "multi_image_source_label": "Multi Image", "multi_image_source": "Multi Image", "input_label": "Input", "output_label": "Output", "size_label": "Size", "width_label": "<PERSON><PERSON><PERSON>", "height_label": "Height", "value_label": "Value", "mask_label": "Mask", "color_label": "Color", "extract_frames_label": "Extract Frames", "ExtractFrames": "Extract Frames", "remove_background_label": "Remove BG", "RemoveBackground": "Remove BG", "process_frames_label": "Process Frames", "ProcessFrames": "Process Frames", "image_resizer_label": "Resize Image", "ImageResizer": "Resize Image", "frame_sampler_label": "<PERSON><PERSON> Frames", "FrameSampler": "<PERSON><PERSON> Frames", "image_placer_label": "Create Atlas", "ImagePlacer": "Create Atlas", "video_label": "Video", "frames_label": "Frames", "atlas_label": "Atlas", "resized_atlas_label": "Resized Atlas", "images_label": "Images", "sampled_frames_label": "Sampled <PERSON><PERSON>", "node.atlas.description": "Creates an atlas from images", "node.remove_bg.description": "Removes background from images", "node.imageplacer.description": "Creates an atlas from images", "node.imageresizer.description": "Resizes an atlas", "node.removebackground.description": "Removes background from images", "node.processframes.description": "Processes and cleans frames", "node.framesampler.description": "Samples frames from a list", "node.extractframes.description": "Extracts frames from video", "node.multi_image_source.description": "Provides multiple image inputs", "node.video_source.description": "Provides video input", "node.image_source.description": "Provides image input", "start_node_label": "Start", "StartNode": "Start", "end_node_label": "End", "EndNode": "End", "start_trigger_label": "Start", "end_trigger_label": "End", "image_output_label": "Image", "video_output_label": "Video", "images_output_label": "Images", "trigger_input_label": "<PERSON><PERSON>", "images_input_label": "Images", "atlas_output_label": "Atlas", "data_input_label": "Data", "system": "System", "node.startnode.description": "Starting point of the workflow", "node.endnode.description": "Ending point of the workflow", "select_project_path": "Select Project Path", "select_project_path_tooltip": "Select the folder where project files will be saved", "no_path_selected": "No location selected yet", "select_project_directory": "Select Project Directory", "project_path_label": "Path", "project_name_label": "Project", "clear_project_tooltip": "Clear project selection", "project_name_input_placeholder": "Enter project name...", "project_name_input_tooltip": "Specify a name for this project", "project_name_required": "Project name is required", "connected_nodes_label": "Connected Nodes", "final_output_label": "Final Output", "no_project_connected": "No project connected", "no_nodes_connected": "No nodes connected", "create_folders_btn": "Create Folders", "create_folders_tooltip": "Create folder for each node", "folders_created_msg": "Folders created successfully"}