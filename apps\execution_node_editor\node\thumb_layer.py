from PyQt5.QtWidgets import QGraphicsWidget, QGraphicsLinearLayout, QGraphicsProxyWidget, QLabel, QGraphicsItem
from PyQt5.QtCore import Qt, QSize, QRectF, QTimer, QPropertyAnimation, QEasingCurve, QPointF
from PyQt5.QtGui import QPixmap, QImage, QPainter, QColor, QPen, QBrush, QPainterPath
from apps.execution_node_editor.localization import i18n
import os

class ThumbLayer(QGraphicsItem):
    """
    Düğümün üst kısmında seçilen içeriğin küçük resim önizlemesini gösteren bağımsız grafik öğesi.
    <PERSON><PERSON>tman, kaynak dü<PERSON>mler<PERSON> (image_source, video_source, multi_image_source) kullanılır.
    """
    def __init__(self, scene=None, parent=None, parent_node=None):
        super().__init__(parent)
        self.scene = scene
        self.parent_node = parent_node

        
        # parent_node referansını güçlü bir şekilde sakla
        if parent_node:
            self._parent_node_ref = parent_node
        self.width = 200
        self.height = 120
        self.border_width = 2
        self.pin_radius = 5
        
        # Animasyon için değişkenler
        self.animation_offset = 0
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(30)  # 30ms aralıklarla güncelle - daha akıcı animasyon
        
        # Düğümün başlık rengini al (varsayılan olarak mavi)
        try:
            # Varsayılan renk
            self.border_color = QColor("#3498db")  # Mavi çerçeve
            
            # Öncelikle title_brush'tan rengi almaya çalış
            if self.parent_node and hasattr(self.parent_node, '_brush_title'):
                self.border_color = self.parent_node._brush_title.color()
            # Eğer title_color varsa, onu kullan
            elif self.parent_node and hasattr(self.parent_node, 'title_color'):
                self.border_color = self.parent_node.title_color
        except Exception as e:
            self.border_color = QColor("#3498db")  # Mavi çerçeve
        
        # Stil ayarları
        self._pen_default = QPen(self.border_color)
        self._pen_default.setWidthF(2.0)
        self._brush_background = QBrush(QColor("#2a2a2a"))
        
        # Z değerini düğümün üstünde olacak şekilde ayarla
        self.setZValue(5)
        
        # Sahneye ekle
        if scene:
            scene.addItem(self)
    
    def boundingRect(self):
        # Pin ve bağlantı çizgisini de içerecek şekilde sınır dikdörtgenini genişlet
        # Animasyon için daha fazla alan bırak
        return QRectF(0, 0, self.width, self.height + 90)  # Düğümün altına doğru daha fazla alan
    
    def paint(self, painter, option, widget=None):
        # Önce arkaplanı çiz
        path_title = QPainterPath()
        path_title.setFillRule(Qt.WindingFill)
        path_title.addRoundedRect(0, 0, self.width, self.height, 8, 8)
        painter.setPen(self._pen_default)
        painter.setBrush(self._brush_background)
        painter.drawPath(path_title.simplified())
        
        # Eğer pixmap varsa göster
        if self.pixmap and not self.pixmap.isNull():
            # Görüntüyü ölçeklendir
            scaled_pixmap = self.pixmap.scaled(
                self.width - 10, 
                self.height - 10,
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            # Görüntüyü ortalayarak çiz
            x = (self.width - scaled_pixmap.width()) / 2
            y = (self.height - scaled_pixmap.height()) / 2
            painter.drawPixmap(int(x), int(y), scaled_pixmap)
        else:
            # Metin göster
            painter.setPen(Qt.white)
            painter.drawText(QRectF(0, 0, self.width, self.height), Qt.AlignCenter, self.title)
        
        # Animasyon değeri kullanılarak çizgi çiz
        animation_offset = getattr(self, 'animation_offset', 0)
        dash_length = 6
        gap_length = 3
        total_pattern_length = dash_length + gap_length
        
        # Bağlantı çizgisini düğümün altına doğru çiz
        # Daha kalın ve daha uzun bir çizgi
        dash_pen = QPen(self.border_color, 2, Qt.CustomDashLine)  # 2 piksel kalınlık
        
        # Animasyon için dash pattern'i kaydır - ters yönde (yukarıdan aşağıya)
        offset = -animation_offset % total_pattern_length  # Eksi işareti akış yönünü tersine çevirir
        dash_pen.setDashPattern([dash_length, gap_length])
        dash_pen.setDashOffset(offset)  # Animasyon için dash offset'i ayarla
        painter.setPen(dash_pen)
        
        # Çizgiyi pin'den kısa bir şekilde aşağıya uzat
        painter.drawLine(
            int(self.width / 2),                    # başlangıç x
            int(self.height + self.pin_radius),    # başlangıç y - pin'in ortasından başla
            int(self.width / 2),                    # bitiş x
            int(self.height + 45)                   # bitiş y - kısa bir çizgi
        )
        
        # Pin'i çiz - resmin altında, tam ortada
        painter.setPen(QPen(self.border_color, 1))
        painter.setBrush(QBrush(self.border_color))
        painter.drawEllipse(
            int(self.width / 2 - self.pin_radius * 1.2), 
            int(self.height),  # Resmin altında
            int(self.pin_radius * 2.4), 
            int(self.pin_radius * 2.4)
        )
        
        # Eğer pixmap varsa göster
        if self.pixmap and not self.pixmap.isNull():
            # Görüntüyü ölçeklendir
            scaled_pixmap = self.pixmap.scaled(
                self.width - 10, 
                self.height - 10,
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            # Görüntüyü ortalayarak çiz
            x = (self.width - scaled_pixmap.width()) / 2
            y = (self.height - scaled_pixmap.height()) / 2
            painter.drawPixmap(int(x), int(y), scaled_pixmap)
        else:
            # Metin göster
            painter.setPen(Qt.white)
            painter.drawText(QRectF(0, 0, self.width, self.height), Qt.AlignCenter, self.title)
        
        # Eğer pixmap varsa göster
        if self.pixmap and not self.pixmap.isNull():
            # Görüntüyü ölçeklendir
            scaled_pixmap = self.pixmap.scaled(
                self.width - 10, 
                self.height - 10,
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            # Görüntüyü ortalayarak çiz
            x = (self.width - scaled_pixmap.width()) / 2
            y = (self.height - scaled_pixmap.height()) / 2
            painter.drawPixmap(int(x), int(y), scaled_pixmap)
        else:
            # Metin göster
            painter.setPen(Qt.white)
            painter.drawText(QRectF(0, 0, self.width, self.height), Qt.AlignCenter, self.title)
    
    def update_animation(self):
        """Animasyon değerini güncelle"""
        # Animasyon değerini sabit hızda artır (akış efekti için)
        self.animation_offset += 0.5  # Daha yavaş ve daha az titreme için küsüratlı artış
        
        # Belirli bir değere ulaşınca sıfırla (döngüsel animasyon)
        if self.animation_offset > 20:
            self.animation_offset = 0
            
        # Yeniden çizim için güncelle
        self.update()
    
    def update_position(self):
        """Düğümün konumuna göre ThumbLayer'i güncelle"""
        if self.parent_node:
            scene_pos = self.parent_node.scenePos()
            node_width = self.parent_node.width
            thumb_x = scene_pos.x() + (node_width - self.width) / 2
            thumb_y = scene_pos.y() - self.height - 35  # Düğümün üstünde göster
            self.setPos(thumb_x, thumb_y)

    def set_pixmap(self, pixmap):
        """ThumbLayer için pixmap ayarla"""
        self.pixmap = pixmap
        self.update()  # Görüntüyü güncellemek için yeniden çiz
    
    def setPixmap(self, pixmap):
        """
        Önizleme görüntüsünü ayarlar
        """
        if pixmap and not pixmap.isNull():
            self.pixmap = pixmap
            self.title = ""
            self.setVisible(True)
            self.update()  # Yeniden çizim için güncelle
            self.update_position()  # Konumu güncelle
        else:
            self.pixmap = None
            self.title = i18n.get('no_preview')
            self.setVisible(False)
    
    def setImagePath(self, image_path):
        """
        Resim yolundan önizleme görüntüsü oluşturur
        """
        if image_path and os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            self.setPixmap(pixmap)
        else:
            self.pixmap = None
            self.title = i18n.get('no_preview')
            self.setVisible(False)
    
    def setVideoPath(self, video_path):
        """
        Video yolundan önizleme görüntüsü oluşturur (ilk kare)
        Not: Bu işlev için OpenCV gerekebilir, şimdilik sadece bir ikon gösteriyoruz
        """
        # Şimdilik sadece video ikonu göster
        icon_path = os.path.join(os.path.dirname(__file__), '../assets/icons/Editor/video.png')
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path)
            self.setPixmap(pixmap)
        else:
            self.pixmap = None
            self.title = i18n.get('video_preview')
            self.setVisible(True)
            self.update()
            self.update_position()
    
    def setMultiImagePaths(self, image_paths):
        """
        Çoklu resim yollarından önizleme görüntüsü oluşturur (ilk resim)
        """
        if image_paths and len(image_paths) > 0:
            # İlk resmi göster
            first_image = image_paths[0]
            if os.path.exists(first_image):
                pixmap = QPixmap(first_image)
                self.setPixmap(pixmap)
                return
        
        # Resim yoksa veya geçersizse
        self.pixmap = None
        self.title = i18n.get('multi_image_preview')
        self.setVisible(True)
        self.update()
        self.update_position()