from PyQt5.QtWidgets import QAction, QMenu
from apps.execution_node_editor.localization import i18n
import sip
from apps.execution_node_editor.ui.preferences_dialog import PreferencesDialog

class MenuManager:
    def __init__(self, window):
        self.window = window
        self.toolbarNodesAction = None
        self._init_menus()
        i18n.languageChanged.connect(self.updateMenus)

    def _init_menus(self):
        self.menubar = self.window.menuBar()
        self.fileMenu = None
        self.editMenu = None
        self.windowMenu = None
        self.shortcutsMenu = None
        self.languageMenu = None
        self.helpMenu = None
        self.lang_en = None
        self.lang_tr = None
        self.createMenus()

    def createMenus(self):
        self.menubar.clear()
        self.window.createFileMenu = self.createFileMenu
        self.window.createEditMenu = self.createEditMenu
        self.createFileMenu()
        self.createEditMenu()
        self.windowMenu = self.menubar.addMenu(i18n.get("menu_window"))
        self.updateWindowMenu()
        self.windowMenu.aboutToShow.connect(self.updateWindowMenu)
        self.menubar.addSeparator()
        self.createPreferencesMenu()
        self.createHelpMenu()
        self.editMenu.aboutToShow.connect(self.window.updateEditMenu)

    def createFileMenu(self):
        self.fileMenu = self.menubar.addMenu(i18n.get('menu_file'))
        self.fileMenu.addAction(self.window.actNew)
        self.fileMenu.addSeparator()
        self.fileMenu.addAction(self.window.actOpen)
        self.fileMenu.addAction(self.window.actSave)
        self.fileMenu.addAction(self.window.actSaveAs)
        self.fileMenu.addSeparator()
        self.fileMenu.addAction(self.window.actExit)

    def createEditMenu(self):
        self.editMenu = self.menubar.addMenu(i18n.get('menu_edit'))
        self.editMenu.addAction(self.window.actUndo)
        self.editMenu.addAction(self.window.actRedo)
        self.editMenu.addSeparator()
        self.editMenu.addAction(self.window.actCut)
        self.editMenu.addAction(self.window.actCopy)
        self.editMenu.addAction(self.window.actPaste)
        self.editMenu.addSeparator()
        self.editMenu.addAction(self.window.actDelete)

    def createShortcutsMenu(self):
        self.shortcutsMenu = self.menubar.addMenu(i18n.get("menu_shortcuts"))
        self.shortcutsMenu.clear()
        self.shortcutsMenu.addAction(i18n.get("shortcut_new"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_open"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_save"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_saveas"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_exit"))
        self.shortcutsMenu.addSeparator()
        self.shortcutsMenu.addAction(i18n.get("shortcut_undo"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_redo"))
        self.shortcutsMenu.addSeparator()
        self.shortcutsMenu.addAction(i18n.get("shortcut_cut"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_copy"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_paste"))
        self.shortcutsMenu.addAction(i18n.get("shortcut_delete"))

    def createLanguageMenu(self):
        self.languageMenu = self.menubar.addMenu(i18n.get("menu_language"))
        self.languageMenu.clear()
        self.lang_en = QAction(i18n.get("language_en"), self.window, checkable=True)
        self.lang_tr = QAction(i18n.get("language_tr"), self.window, checkable=True)
        self.lang_en.setChecked(i18n.get_language() == "en")
        self.lang_tr.setChecked(i18n.get_language() == "tr")
        self.lang_en.triggered.connect(lambda: i18n.set_language("en"))
        self.lang_tr.triggered.connect(lambda: i18n.set_language("tr"))
        self.languageMenu.addAction(self.lang_en)
        self.languageMenu.addAction(self.lang_tr)

    def createHelpMenu(self):
        self.helpMenu = self.menubar.addMenu(i18n.get("menu_help"))
        self.helpMenu.addAction(self.window.actAbout)

    def createPreferencesMenu(self):
        self.preferencesMenu = self.menubar.addMenu(i18n.get("menu_preferences"))
        self.preferencesMenu.clear()
        self.preferencesMenu.addAction(i18n.get("menu_preferences"), self.show_preferences_dialog)

    def show_preferences_dialog(self):
        dlg = PreferencesDialog(self.window)
        dlg.exec_()

    def updateActionTexts(self):
        # File actions
        self.window.actNew.setText(i18n.get("action_new"))
        self.window.actOpen.setText(i18n.get("action_open"))
        self.window.actSave.setText(i18n.get("action_save"))
        self.window.actSaveAs.setText(i18n.get("action_saveas"))
        self.window.actExit.setText(i18n.get("action_exit"))
        # Edit actions
        self.window.actUndo.setText(i18n.get("action_undo"))
        self.window.actRedo.setText(i18n.get("action_redo"))
        self.window.actCut.setText(i18n.get("action_cut"))
        self.window.actCopy.setText(i18n.get("action_copy"))
        self.window.actPaste.setText(i18n.get("action_paste"))
        self.window.actDelete.setText(i18n.get("action_delete"))
        # Window actions (bazıları menüde özel olarak ekleniyor)
        if hasattr(self.window, 'actClose'):
            self.window.actClose.setText(i18n.get("window_close"))
        if hasattr(self.window, 'actCloseAll'):
            self.window.actCloseAll.setText(i18n.get("window_close_all"))
        if hasattr(self.window, 'actTile'):
            self.window.actTile.setText(i18n.get("window_tile"))
        if hasattr(self.window, 'actCascade'):
            self.window.actCascade.setText(i18n.get("window_cascade"))
        if hasattr(self.window, 'actNext'):
            self.window.actNext.setText(i18n.get("window_next"))
        if hasattr(self.window, 'actPrevious'):
            self.window.actPrevious.setText(i18n.get("window_previous"))
        if hasattr(self.window, 'actAbout'):
            self.window.actAbout.setText(i18n.get("about_text"))

    def updateMenus(self):
        self.updateActionTexts()
        if self.fileMenu:
            self.fileMenu.setTitle(i18n.get("menu_file"))
        if self.editMenu:
            self.editMenu.setTitle(i18n.get("menu_edit"))
        if self.windowMenu:
            self.windowMenu.setTitle(i18n.get("menu_window"))
        if self.preferencesMenu:
            self.preferencesMenu.setTitle(i18n.get("menu_preferences"))
        if self.shortcutsMenu:
            self.shortcutsMenu.setTitle(i18n.get("menu_shortcuts"))
            self.shortcutsMenu.clear()
            self.createShortcutsMenu()
        if self.languageMenu:
            self.languageMenu.setTitle(i18n.get("menu_language"))
            self.lang_en.setText(i18n.get("language_en"))
            self.lang_tr.setText(i18n.get("language_tr"))
            self.lang_en.setChecked(i18n.get_language() == "en")
            self.lang_tr.setChecked(i18n.get_language() == "tr")
        if self.helpMenu:
            self.helpMenu.setTitle(i18n.get("menu_help"))

    def updateWindowMenu(self):
        # QMdiArea silinmiş mi kontrolü
        if not hasattr(self.window, 'mdiArea') or self.window.mdiArea is None or sip.isdeleted(self.window.mdiArea):
            return
        self.windowMenu.clear()
        # Düğümler Araç Çubuğu QAction'ı canlı localize
        if self.toolbarNodesAction is None:
            self.toolbarNodesAction = QAction(i18n.get("window_nodes_toolbar"), self.window)
            self.toolbarNodesAction.setCheckable(True)
            self.toolbarNodesAction.triggered.connect(self.window.onWindowNodesToolbar)
        else:
            self.toolbarNodesAction.setText(i18n.get("window_nodes_toolbar"))
        self.toolbarNodesAction.setChecked(self.window.nodesDock.isVisible())
        self.windowMenu.addAction(self.toolbarNodesAction)
        self.windowMenu.addSeparator()
        self.windowMenu.addAction(self.window.actClose)
        self.windowMenu.addAction(self.window.actCloseAll)
        self.windowMenu.addSeparator()
        self.windowMenu.addAction(self.window.actTile)
        self.windowMenu.addAction(self.window.actCascade)
        self.windowMenu.addSeparator()
        self.windowMenu.addAction(self.window.actNext)
        self.windowMenu.addAction(self.window.actPrevious)
        self.windowMenu.addAction(self.window.actSeparator)
        windows = self.window.mdiArea.subWindowList()
        self.window.actSeparator.setVisible(len(windows) != 0)
        for i, window in enumerate(windows):
            child = window.widget()
            text = "%d %s" % (i + 1, child.getUserFriendlyFilename())
            if i < 9:
                text = '&' + text
            action = self.windowMenu.addAction(text)
            action.setCheckable(True)
            action.setChecked(child is self.window.getCurrentNodeEditorWidget())
            action.triggered.connect(self.window.windowMapper.map)
            self.window.windowMapper.setMapping(action, window)
        # Menü hover efekti için QSS uygula
        self.windowMenu.setStyleSheet("""
            QMenu::item:selected {
                background: #1976d2;
                color: #fff;
            }
        """) 