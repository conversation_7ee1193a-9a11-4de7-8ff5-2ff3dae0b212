# Contributing 

Bug fixes, feature additions, tests, documentation and more can be contributed 
via [issues](https://github.com/beyse/NodeEditor/issues) and/or [pull_requests](https://github.com/beyse/NodeEditor/pulls). All contributions are welcome.

## Bug fixes, feature additions, etc.

Please send a pull request to the main branch. Please include documentation. Tests or documentation without bug fixes or feature additions are welcome too. Feel free to ask questions [via issues](https://github.com/beyse/NodeEditor/issues) 

- Create a branch from main.
- Develop bug fixes, features, tests, etc.
- Test your code on Python 3.x. 
- Create a pull request to merge the changes from your branch to the ExecutionNodeEditor main.

### Guidelines

- Separate code commits from reformatting commits.
- Provide tests for any newly added code when possible.

## Reporting Issues

When reporting issues, please include code that reproduces the issue and whenever possible, an image that demonstrates the issue. Please upload images to GitHub, not to third-party file hosting sites. If necessary, add the image to a zip or tar archive.

The best reproductions are self-contained scripts with minimal dependencies. If you are using a framework, try to replicate the issue just using ExecutionNodeEditor.

### Provide details

- What did you do?
- What did you expect to happen?
- What actually happened?
- What versions of ExecutionNodeEditor and Python are you using?
