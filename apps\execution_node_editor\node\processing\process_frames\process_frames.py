import cv2
import numpy as np
import os
import glob
import argparse # Import argparse
from rembg import remove, new_session # new_session eklendi, farklı modeller i<PERSON><PERSON> kull<PERSON>bil<PERSON>

def process_single_image(input_image_path, output_image_path, model_name="u2net"):
    """
    Removes background and a watermark from a single image and saves it to output_image_path.
    Uses a specified rembg model.
    """
    try:
        if not os.path.exists(input_image_path):
            pass
            return False

        img = cv2.imread(input_image_path, cv2.IMREAD_UNCHANGED)
        if img is None:
            pass
            return False

        # rembg için BGR formatı sağla
        if len(img.shape) > 2 and img.shape[2] == 4: # BGRA ise BGR'ye çevir
            input_img_for_rembg = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        else: # Zaten BGR veya gri tonlamalı ise (rembg BGR bekler)
            input_img_for_rembg = img

        # Belirtilen model için rembg session oluştur
        session = new_session(model_name)
        
        # Arka planı kaldır (BGRA döner)
        img_no_bg = remove(input_img_for_rembg, session=session)

        if img_no_bg is None or (len(img_no_bg.shape) > 2 and img_no_bg.shape[2] != 4):
             pass
             return False
        
        img_processed = img_no_bg.copy() # Üzerinde değişiklik yapmak için kopyala

        # Filigran Kaldırma (Arka plan kaldırıldıktan sonra)
        height, width = img_processed.shape[:2]
        watermark_height = 50  # Alttan piksel
        watermark_width = 150 # Sağdan piksel

        y_start = max(0, height - watermark_height)
        x_start = max(0, width - watermark_width)

        # Filigran bölgesini şeffaf yap (Alfa kanalını 0 yap)
        if len(img_processed.shape) > 2 and img_processed.shape[2] == 4: # Alfa kanalı varsa
            img_processed[y_start:height, x_start:width, 3] = 0
        else:
            pass


        # İşlenmiş resmi kaydet
        # Çıktı klasörünün var olduğundan emin ol
        output_folder = os.path.dirname(output_image_path)
        if output_folder and not os.path.exists(output_folder):
            try:
                os.makedirs(output_folder)
            except OSError as e:
                pass
                return False
        
        write_success = cv2.imwrite(output_image_path, img_processed)
        if not write_success:
             pass
             return False
        
        return True

    except Exception as e:
        pass
        return False

def process_images_in_folder(input_folder, output_folder, model_name="u2net"):
    """Processes all PNG images in input_folder and saves them to output_folder."""
    if not os.path.isdir(input_folder):
        pass
        return 0
    
    if not os.path.exists(output_folder):
        try:
            os.makedirs(output_folder)
        except OSError as e:
            pass
            return 0
            
    image_files = glob.glob(os.path.join(input_folder, '*.png'))
    if not image_files:
        pass
        return 0
        
    processed_count = 0
    for image_path in image_files:
        base_name = os.path.basename(image_path)
        output_image_path = os.path.join(output_folder, base_name)
        
        if process_single_image(image_path, output_image_path, model_name):
            processed_count += 1
            
    return processed_count

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bir resmin veya bir klasördeki tüm PNG resimlerinin arka planını ve filigranını kaldırır.")
    parser.add_argument("input_path", help="İşlenecek resim dosyasının veya resimleri içeren klasörün yolu.")
    parser.add_argument("output_path", help="İşlenmiş resmin veya resimlerin kaydedileceği dosya/klasör yolu.")
    parser.add_argument("-m", "--model", default="u2net", help="Kullanılacak rembg modeli (örn: u2net, u2netp, isnet-general-use, silueta). Varsayılan: u2net")
    
    args = parser.parse_args()

    if os.path.isfile(args.input_path):
        # Tek dosya işleme
        output_dir = os.path.dirname(args.output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        process_single_image(args.input_path, args.output_path, args.model)
    elif os.path.isdir(args.input_path):
        # Klasör işleme
        process_images_in_folder(args.input_path, args.output_path, args.model)
    else:
        pass

