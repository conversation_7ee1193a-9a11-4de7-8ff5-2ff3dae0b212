#!/usr/bin/env python3
"""
Script to safely remove print statements from Python files
"""
import os
import re
import ast
import shutil
import argparse

def is_valid_python(content):
    """Check if content is valid Python syntax"""
    try:
        ast.parse(content)
        return True
    except SyntaxError:
        return False

def create_backup(file_path):
    """Create a backup of the file"""
    backup_path = file_path + '.backup'
    shutil.copy2(file_path, backup_path)
    return backup_path

def clean_print_statements(file_path, dry_run=False):
    """Safely remove print statements from a Python file"""
    try:
        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        content = None
        used_encoding = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print(f"❌ <PERSON>sy<PERSON>mad<PERSON> (encoding sorunu): {file_path}")
            return False, 0

        # Check if file is valid Python before processing
        if not is_valid_python(content):
            print(f"❌ Geçersiz Python dosyası: {file_path}")
            return False, 0

        lines = content.split('\n')
        cleaned_lines = []
        removed_count = 0

        # Patterns to NOT remove (preserve these)
        preserve_patterns = [
            r'def\s+print\s*\(',  # print function definitions
            r'class.*print',      # class names containing print
            r'#.*print',          # comments containing print
            r'""".*print.*"""',   # docstrings containing print
            r"'''.*print.*'''",   # docstrings containing print
            r'if\s+DEBUG\s*:\s*print\s*\(',  # DEBUG print calls - preserve these
            r'if\s+debug\s*:\s*print\s*\(',  # debug print calls - preserve these
            r'lambda.*print\s*\(',  # lambda functions with print
        ]

        for i, line in enumerate(lines):
            should_remove = False

            # Check if line should be preserved
            preserve_line = False
            for preserve_pattern in preserve_patterns:
                if re.search(preserve_pattern, line, re.IGNORECASE):
                    preserve_line = True
                    break

            if not preserve_line:
                # Only remove single-line print statements that are complete
                if re.search(r'^\s*print\s*\(.*\)\s*$', line):
                    should_remove = True

            if should_remove:
                removed_count += 1
                if dry_run:
                    print(f"  🔍 Satır {i+1}: {line.strip()}")
                # Replace with pass if this would leave an empty block
                if i > 0:
                    prev_line = lines[i-1].strip()

                    # Check if removing this line would leave an empty block
                    if (prev_line.endswith(':') or
                        prev_line.endswith('except:') or
                        prev_line.endswith('except Exception:') or
                        'except' in prev_line and prev_line.endswith(':')):
                        # Add pass statement instead of removing completely
                        cleaned_lines.append(line.replace(line.strip(), 'pass'))
                    else:
                        # Safe to remove completely
                        pass
                else:
                    # Safe to remove completely
                    pass
            else:
                cleaned_lines.append(line)

        if removed_count > 0:
            if not dry_run:
                # Create backup before modifying
                backup_path = create_backup(file_path)

                # Join lines and ensure proper line endings
                cleaned_content = '\n'.join(cleaned_lines)

                # Validate cleaned content
                if not is_valid_python(cleaned_content):
                    print(f"❌ Temizleme sonrası geçersiz Python kodu: {file_path}")
                    print("   Backup geri yükleniyor...")
                    shutil.copy2(backup_path, file_path)
                    os.remove(backup_path)
                    return False, 0

                # Write cleaned content
                with open(file_path, 'w', encoding=used_encoding) as f:
                    f.write(cleaned_content)

                # Remove backup if successful
                os.remove(backup_path)
                print(f"✅ Temizlendi: {file_path} ({removed_count} print statement silindi)")
            else:
                print(f"🔍 Bulundu: {file_path} ({removed_count} print statement)")

        return True, removed_count

    except Exception as e:
        print(f"❌ Hata: {file_path} - {str(e)}")
        return False, 0

def find_python_files(directory):
    """Find all Python files in directory and subdirectories"""
    python_files = []
    script_name = os.path.basename(__file__)  # Bu scriptin adı

    for root, dirs, files in os.walk(directory):
        # Skip virtual environments and cache directories
        dirs[:] = [d for d in dirs if d not in ['venv', '.venv', '__pycache__', '.git', 'node_modules']]

        for file in files:
            if file.endswith('.py') and file != script_name:  # Bu scripti hariç tut
                python_files.append(os.path.join(root, file))

    return python_files

def scan_for_remaining_prints(directory):
    """Scan for any remaining print statements"""
    python_files = find_python_files(directory)
    remaining_prints = []

    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for i, line in enumerate(lines):
                if re.search(r'print\s*\(', line) and not re.search(r'def\s+print\s*\(', line):
                    remaining_prints.append((file_path, i+1, line.strip()))
        except:
            continue

    return remaining_prints

def main():
    parser = argparse.ArgumentParser(description='Python dosyalarındaki print statement\'larını güvenli şekilde temizler')
    parser.add_argument('directory', nargs='?', default='.', help='Taranacak dizin (varsayılan: mevcut dizin)')
    parser.add_argument('--dry-run', action='store_true', help='Sadece tarama yap, değişiklik yapma')
    parser.add_argument('--scan-only', action='store_true', help='Sadece kalan print statement\'ları tara')

    args = parser.parse_args()

    if args.scan_only:
        print("🔍 Kalan print statement'ları taranıyor...")
        remaining = scan_for_remaining_prints(args.directory)
        if remaining:
            print(f"⚠️  {len(remaining)} print statement bulundu:")
            for file_path, line_num, line_content in remaining:
                rel_path = os.path.relpath(file_path)
                print(f"  ⚠️  Found print in {rel_path}:{line_num}: {line_content}")
        else:
            print("✅ Hiç print statement bulunamadı!")
        return

    print("🧹 Python Print Statement Temizleyici")
    print("=" * 50)

    if args.dry_run:
        print("🔍 DRY RUN MODE - Hiçbir değişiklik yapılmayacak")
        print()

    # Find all Python files
    python_files = find_python_files(args.directory)
    print(f"📁 {len(python_files)} Python dosyası bulundu")
    print()

    total_processed = 0
    total_cleaned = 0
    total_removed = 0

    for file_path in python_files:
        rel_path = os.path.relpath(file_path)
        success, removed_count = clean_print_statements(file_path, args.dry_run)

        if success:
            total_processed += 1
            if removed_count > 0:
                total_cleaned += 1
                total_removed += removed_count

    print()
    print("📊 Özet:")
    print(f"İşlenen toplam Python dosyası: {total_processed}")
    print(f"Temizlenen toplam dosya: {total_cleaned}")
    print(f"Silinen toplam print statement: {total_removed}")

    if not args.dry_run and total_removed > 0:
        print()
        print("🔍 Kalan print statement'ları kontrol ediliyor...")
        remaining = scan_for_remaining_prints(args.directory)
        if remaining:
            print(f"⚠️  {len(remaining)} print statement bulundu")
            print("Manuel inceleme gerekebilir.")
        else:
            print("✅ Tüm print statement'lar başarıyla temizlendi!")

if __name__ == "__main__":
    main()
