import cv2
import os
import argparse

def extract_frames(video_path, output_dir_override=None):
    """
    Extracts frames from a single video file.
    If output_dir_override is provided, frames are saved there.
    Otherwise, a folder named after the video is created in the video's directory.
    """
    if not os.path.exists(video_path):
        pass
        return False, 0

    if output_dir_override:
        output_folder = output_dir_override
    else:
        # Extract video name without extension to use as folder name
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        # Use current directory as base for the output folder if not overridden
        output_folder = os.path.join(os.path.dirname(video_path) if os.path.dirname(video_path) else '.', video_name)

    # Create the output folder if it doesn't exist
    if not os.path.exists(output_folder):
        try:
            os.makedirs(output_folder)
        except OSError as e:
            pass
            return False, 0
    else:
        pass


    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        pass
        return False, 0

    frame_count = 0
    success_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            if frame_count > 0:
                 pass
            else:
                 pass
            break

        frame_count += 1
        frame_filename = os.path.join(output_folder, f"frame_{frame_count:04d}.png")

        try:
            write_success = cv2.imwrite(frame_filename, frame)
            if not write_success:
                pass
            else:
                success_count += 1
        except Exception as e:
             pass

        if success_count > 0 and success_count % 100 == 0:
            pass

    cap.release()
    return True, success_count

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bir video dosyasının karelerini PNG olarak çıkarır.")
    parser.add_argument("video_file", help="İşlenecek video dosyasının yolu (.mp4)")
    parser.add_argument("-o", "--output_directory", help="Karelerin kaydedileceği çıktı klasörü (isteğe bağlı). Belirtilmezse video adıyla aynı dizinde bir klasör oluşturulur.")
    args = parser.parse_args()

    extract_frames(args.video_file, args.output_directory)
