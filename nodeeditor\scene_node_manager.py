"""
Scene node ve edge y<PERSON><PERSON><PERSON><PERSON> için ayrı modül
"""
from typing import List, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal


class SceneNodeManager(QObject):
    """Scene'deki node ve edge'leri yöneten sınıf"""
    
    # Sinyaller
    node_added = pyqtSignal(object)  # Node eklendi
    node_removed = pyqtSignal(object)  # Node kaldırıldı
    edge_added = pyqtSignal(object)  # Edge eklendi
    edge_removed = pyqtSignal(object)  # Edge kaldırıldı
    scene_cleared = pyqtSignal()  # Scene temizlendi
    
    def __init__(self, scene, parent=None):
        super().__init__(parent)
        self.scene = scene
        self.debug_remove_warnings = False
        
        # Node class selector function
        self.node_class_selector = None
    
    def add_node(self, node):
        """Scene'e node ekler"""
        if node not in self.scene.nodes:
            self.scene.nodes.append(node)
            self.node_added.emit(node)
            self._mark_scene_modified()
    
    def remove_node(self, node):
        """Scene'den node kaldırır"""
        if node in self.scene.nodes:
            self.scene.nodes.remove(node)
            self.node_removed.emit(node)
            self._mark_scene_modified()
        else:
            if self.debug_remove_warnings:
                pass
    
    def add_edge(self, edge):
        """Scene'e edge ekler"""
        if edge not in self.scene.edges:
            self.scene.edges.append(edge)
            self.edge_added.emit(edge)
            self._mark_scene_modified()
    
    def remove_edge(self, edge):
        """Scene'den edge kaldırır"""
        if edge in self.scene.edges:
            self.scene.edges.remove(edge)
            self.edge_removed.emit(edge)
            self._mark_scene_modified()
        else:
            if self.debug_remove_warnings:
                pass
    
    def get_node_by_id(self, node_id: int):
        """ID'ye göre node bulur"""
        for node in self.scene.nodes:
            if node.id == node_id:
                return node
        return None
    
    def get_nodes_by_type(self, node_type: str) -> List:
        """Türe göre node'ları bulur"""
        return [node for node in self.scene.nodes if hasattr(node, 'node_type') and node.node_type == node_type]
    
    def get_nodes_by_title(self, title: str) -> List:
        """Başlığa göre node'ları bulur"""
        return [node for node in self.scene.nodes if hasattr(node, 'title') and node.title == title]
    
    def get_connected_nodes(self, node) -> List:
        """Belirli bir node'a bağlı node'ları bulur"""
        connected_nodes = set()
        
        for edge in self.scene.edges:
            if hasattr(edge, 'start_socket') and hasattr(edge, 'end_socket'):
                if edge.start_socket and edge.end_socket:
                    if edge.start_socket.node == node:
                        connected_nodes.add(edge.end_socket.node)
                    elif edge.end_socket.node == node:
                        connected_nodes.add(edge.start_socket.node)
        
        return list(connected_nodes)
    
    def get_edges_for_node(self, node) -> List:
        """Belirli bir node'un edge'lerini bulur"""
        node_edges = []
        
        for edge in self.scene.edges:
            if hasattr(edge, 'start_socket') and hasattr(edge, 'end_socket'):
                if edge.start_socket and edge.end_socket:
                    if edge.start_socket.node == node or edge.end_socket.node == node:
                        node_edges.append(edge)
        
        return node_edges
    
    def clear_scene(self):
        """Scene'i temizler (tüm node'ları kaldırır)"""
        # Node'ları kopyala çünkü remove işlemi sırasında liste değişecek
        nodes_to_remove = self.scene.nodes.copy()

        for node in nodes_to_remove:
            if hasattr(node, 'remove'):
                node.remove()

        # Eğer hala node kaldıysa, manuel olarak temizle
        self.scene.nodes.clear()
        self.scene.edges.clear()

        self.scene.has_been_modified = False
        self.scene_cleared.emit()
    
    def set_node_class_selector(self, class_selecting_function: Callable):
        """Node class selector function'ını ayarlar"""
        self.node_class_selector = class_selecting_function
    
    def get_node_class(self, node_data: dict):
        """Node data'sından node class'ını belirler"""
        if self.node_class_selector:
            return self.node_class_selector(node_data)
        
        # Varsayılan olarak ConcreteExecutionNode kullan
        from apps.execution_node_editor.conf import ConcreteExecutionNode
        return ConcreteExecutionNode
    
    def validate_connections(self) -> List[str]:
        """Bağlantıları doğrular ve hataları döndürür"""
        errors = []
        
        for edge in self.scene.edges:
            if not hasattr(edge, 'start_socket') or not edge.start_socket:
                errors.append(f"Edge {edge.id} has no start socket")
            
            if not hasattr(edge, 'end_socket') or not edge.end_socket:
                errors.append(f"Edge {edge.id} has no end socket")
            
            if edge.start_socket and edge.end_socket:
                if edge.start_socket.node not in self.scene.nodes:
                    errors.append(f"Edge {edge.id} start node not in scene")
                
                if edge.end_socket.node not in self.scene.nodes:
                    errors.append(f"Edge {edge.id} end node not in scene")
        
        return errors
    
    def get_scene_statistics(self) -> dict:
        """Scene istatistiklerini döndürür"""
        node_types = {}
        for node in self.scene.nodes:
            node_type = getattr(node, 'node_type', 'Unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        return {
            'total_nodes': len(self.scene.nodes),
            'total_edges': len(self.scene.edges),
            'node_types': node_types,
            'validation_errors': len(self.validate_connections())
        }
    
    def _mark_scene_modified(self):
        """Scene'i değiştirilmiş olarak işaretle"""
        if hasattr(self.scene, 'has_been_modified'):
            self.scene.has_been_modified = True
    
    def cleanup(self):
        """Kaynakları temizler"""
        self.node_class_selector = None
