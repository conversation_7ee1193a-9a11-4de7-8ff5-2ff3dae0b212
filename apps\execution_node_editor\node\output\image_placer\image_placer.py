from PIL import Image
import os
import glob # Dosya arama için
import math # Matematiksel hesaplamalar için (sqrt, ceil)
import re # Sıralama için regex
import argparse # Komut satırı argümanları için

def place_images_on_canvas(canvas_width, canvas_height, image_data, output_path):
    """
    Verilen resimleri belirtilen koordinatlara şeffaf bir tuval üzerine yerleştirir
    ve sonucu PNG olarak kaydeder.

    Args:
        canvas_width (int): <PERSON><PERSON><PERSON> g<PERSON> (piksel).
        canvas_height (int): <PERSON><PERSON><PERSON> yü<PERSON>ğ<PERSON> (piksel).
        image_data (list): Her biri {'path': str, 'x': int, 'y': int} formatında
                           sözlükler içeren bir liste. 'path' resim dosyasının yolu,
                           'x' ve 'y' ise tuval üzerindeki sol üst köşe koordinatlarıdır.
        output_path (str): <PERSON><PERSON><PERSON> kayded<PERSON>ği PNG dosyasının yolu.
    """
    # Şeffaf (RGBA) bir tuval oluştur
    # Renk (0, 0, 0, 0) tamamen şeffaf siyahtır
    canvas = Image.new('RGBA', (canvas_width, canvas_height), (0, 0, 0, 0))

    # Resimleri tuvale yerleştir
    for item in image_data:
        try:
            img_path = item['path']
            x = item['x']
            y = item['y']

            if not os.path.exists(img_path):
                pass
                continue

            img = Image.open(img_path).convert("RGBA") # Şeffaflığı korumak için RGBA'ya dönüştür

            # Resmi tuvale yapıştır. Maske olarak resmin alfa kanalını kullan.
            # Bu, resmin şeffaf kısımlarının tuvalde de şeffaf kalmasını sağlar.
            canvas.paste(img, (x, y), img)

            img.close() # Belleği boşaltmak için resmi kapat

        except FileNotFoundError:
            pass
        except KeyError as e:
            pass
        except Exception as e:
            pass

    # Sonucu PNG olarak kaydet
    try:
        canvas.save(output_path, 'PNG')
    except Exception as e:
        pass

    canvas.close() # Tuvali kapat
    return True # Başarı durumunu döndür

# --- Belirli bir klasör için Atlas Oluşturma Fonksiyonu ---
def create_atlas_for_directory(input_dir, output_atlas_path=None):
    """
    Belirtilen klasördeki .png/.PNG dosyalarından bir sprite atlası oluşturur.
    Eğer output_atlas_path belirtilmemişse, atlası girdi klasörüyle aynı dizine
    KLASORADI_atlas.png olarak kaydeder. Belirtilmişse, o yola kaydeder.
    """

    if not os.path.isdir(input_dir):
        pass
        return False

    # Aranacak resim deseni (klasör içinde) - tüm .png ve .PNG dosyalarını case-insensitive olarak dahil et
    IMAGE_PATTERN = os.path.join(input_dir, '*.[pP][nN][gG]')
    
    if output_atlas_path:
        # Çıktı klasörünün var olduğundan emin ol
        output_folder_for_atlas = os.path.dirname(output_atlas_path)
        if output_folder_for_atlas and not os.path.exists(output_folder_for_atlas):
            try:
                os.makedirs(output_folder_for_atlas)
            except OSError as e:
                pass
                return False
        final_output_filename = output_atlas_path
    else:
        # Çıktı dosyasını klasör adıyla ana dizine (veya betiğin çalıştığı yere) kaydet
        output_filename_base = os.path.basename(os.path.normpath(input_dir)) # Klasör adını al
        final_output_filename = f"{output_filename_base}_atlas.png"
        # Eğer input_dir göreceli bir yolsa, bu betiğin çalıştığı yere göre bir yol oluşturur.
        # Eğer input_dir mutlak bir yolsa, o dizinin bir üstüne atar.
        # Daha tutarlı olması için, input_dir'in parent'ına kaydedebiliriz.
        # Veya kullanıcı her zaman output_atlas_path versin. Şimdilik basit tutalım.
        # final_output_filename = os.path.join(os.path.dirname(input_dir) if os.path.dirname(input_dir) else '.', f"{output_filename_base}_atlas.png")


    # Resim dosyalarını bul
    image_files = glob.glob(IMAGE_PATTERN)

    if not image_files:
        pass
        return False

    try:
        sorted_images = sorted(image_files)
    except Exception as e:
        pass
        return False

    if not sorted_images: # Bu kontrol yukarıda image_files ile yapıldı ama yine de kalsın.
        return False
        
    try:
        with Image.open(sorted_images[0]) as ref_img:
            sprite_width, sprite_height = ref_img.size
        if sprite_width <= 0 or sprite_height <= 0:
            pass
            return False
    except FileNotFoundError:
        pass
        return False
    except Exception as e:
        pass
        return False

    num_images = len(sorted_images)

    cols = math.ceil(math.sqrt(num_images))
    rows = math.ceil(num_images / cols)

    canvas_width = cols * sprite_width
    canvas_height = rows * sprite_height

    images_to_place = []
    for i, img_path in enumerate(sorted_images):
        row_index = i // cols
        col_index = i % cols
        x = col_index * sprite_width
        y = row_index * sprite_height
        images_to_place.append({'path': img_path, 'x': x, 'y': y})

    if place_images_on_canvas(canvas_width, canvas_height, images_to_place, final_output_filename):
        pass
        return True
    else:
        pass
        return False

# --- Komut Satırı Argüman İşleme ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Belirtilen klasördeki .png/.PNG dosyalarından bir sprite atlası oluşturur.")
    parser.add_argument("input_directory", help="Sprite'ları içeren klasörün yolu.")
    parser.add_argument("-o", "--output_file", help="Oluşturulacak atlas dosyasının tam yolu (isteğe bağlı). Belirtilmezse, girdi klasörüyle aynı dizine KLASORADI_atlas.png olarak kaydedilir.")
    args = parser.parse_args()

    create_atlas_for_directory(args.input_directory, args.output_file)
