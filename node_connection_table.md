# Node Bağlantı Tablosu

## Resim İşleme Editörü Node Bağlantıları

| Node | Giriş Soketi | Çıkış Soketi | Bağlanabileceği Nodlar |
|------|-------------|-------------|----------------------|
| **START** | 0 | 1 | Resim Kaynağı, Çoklu Resim Kaynağı, Video Kaynağı |
| **Resim Kaynağı** | 1 | 1 | Arka Plan Temizleme, Resim Boyutlandırma, END |
| **Çoklu Resim Kaynağı** | 1 | 1 | Arka Plan Temizleme, Resim Boyutlandırma, Atlas Yapma, END |
| **Video Kaynağı** | 1 | 1 | Kareler Çıkarma, END |
| **Arka Plan Temizleme** | 1 | 1 | <PERSON>si<PERSON>ırma, Atlas Yapma, END |
| **Resim Boyutlandırma** | 1 | 1 | Atlas Yapma, Arka Plan Temizleme, END |
| **Atlas Yapma** | 1 | 1 | END |
| **Kareler Çıkarma** | 1 | 1 | Arka Plan Temizleme, Resim Boyutlandırma, Atlas Yapma, END |
| **END** | 1 | 0 | - (Son node) |

## Soket Açıklamaları

- **Giriş Soketi**: Node'a kaç bağlantı gelebileceği
- **Çıkış Soketi**: Node'dan kaç bağlantı çıkabileceği
- START node'u sadece tetikleyici olduğu için giriş soketi yok
- END node'u son node olduğu için çıkış soketi yok

## Örnek Bağlantı Zincirleri

### Basit Resim İşleme
```
START → Resim Kaynağı → Arka Plan Temizleme → Resim Boyutlandırma → END
```

### Video'dan Atlas Oluşturma
```
START → Video Kaynağı → Kareler Çıkarma → Arka Plan Temizleme → Atlas Yapma → END
```

### Toplu Resim İşleme
```
START → Çoklu Resim Kaynağı → Resim Boyutlandırma → Atlas Yapma → END
```