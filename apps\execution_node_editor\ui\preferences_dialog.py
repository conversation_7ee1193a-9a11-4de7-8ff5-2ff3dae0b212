from PyQt5.QtWidgets import (
    Q<PERSON><PERSON>og, QTabWidget, QVBoxLayout, QWidget, QLabel, QComboBox, QHBoxLayout,
    QPushButton, QTreeWidget, QTreeWidgetItem, QSpacerItem, QSizePolicy, QHeaderView
)
from PyQt5.QtCore import Qt, QSize, QSettings, QTimer
from PyQt5.QtGui import QIcon, QKeySequence, QColor, QPainter, QPixmap
from apps.execution_node_editor.localization import i18n
import os

class PreferencesDialog(QDialog):
    def __init__(self, parent=None, actions=None):
        super().__init__(parent)
        self.setWindowTitle(i18n.get("menu_preferences"))
        self.setMinimumSize(700, 500)
        self.layout = QVBoxLayout(self)
        self.tabs = QTabWidget()
        self.tabs.setIconSize(QSize(12, 12))
        self.layout.addWidget(self.tabs)
        self.actions = actions or {}
        self.init_language_tab()
        self.init_shortcuts_tab()
        self.init_buttons()
        self._original_lang = i18n.get_language()
        self._pending_lang = self._original_lang
        self._editing_row = None
        self._settings = QSettings("NodeEditor", "Shortcuts")
        i18n.languageChanged.connect(self.update_ui_texts)
        self.update_ui_texts()
        self._recording_icon_timer = QTimer(self)
        self._recording_icon_timer.setInterval(500)  # 500ms yanıp sönme
        self._recording_icon_timer.timeout.connect(self._toggle_recording_icon)
        self._recording_icon_bright = True
        self._recording_row_anim = None

    def init_language_tab(self):
        lang_tab = QWidget()
        lang_layout = QVBoxLayout()
        self.lang_title = QLabel(f"<b>{i18n.get('menu_language')}</b>")
        self.lang_title.setStyleSheet("font-size: 16px; margin-bottom: 8px;")
        self.lang_title.setObjectName("preferences_lang_title_objname")
        lang_layout.addWidget(self.lang_title)
        self.lang_desc = QLabel(i18n.get("language_desc"))
        self.lang_desc.setStyleSheet("color: #aaa; margin-bottom: 16px;")
        self.lang_desc.setObjectName("preferences_lang_desc_objname")
        lang_layout.addWidget(self.lang_desc)
        self.lang_combo = QComboBox()
        self.lang_combo.addItem("🇬🇧  " + i18n.get("language_en"), "en")
        self.lang_combo.addItem("🇹🇷  " + i18n.get("language_tr"), "tr")
        self.lang_combo.setCurrentIndex(0 if i18n.get_language() == "en" else 1)
        self.lang_combo.currentIndexChanged.connect(self.on_language_changed)
        self.lang_combo.setObjectName("preferences_lang_combo_objname")
        lang_layout.addWidget(self.lang_combo)
        lang_layout.addStretch()
        lang_tab.setLayout(lang_layout)
        icon_path = "apps/execution_node_editor/assets/icons/Editor/settings.png"
        self.tabs.addTab(lang_tab, QIcon(icon_path), i18n.get("menu_language"))
        self.lang_tab_idx = self.tabs.count() - 1

    def on_language_changed(self, idx):
        self._pending_lang = self.lang_combo.currentData()
        self.apply_btn.setEnabled(self._pending_lang != i18n.get_language())

    def init_shortcuts_tab(self):
        shortcuts_tab = QWidget()
        layout = QVBoxLayout()
        self.shortcuts_tree = QTreeWidget()
        self.shortcuts_tree.setHeaderLabels([i18n.get("menu_shortcuts"), i18n.get("shortcut_key")])
        self.shortcut_items = []
        self.default_shortcuts = {
            "actExit": "Ctrl+Q",
            "actNew": "Ctrl+N",
            "actOpen": "Ctrl+O",
            "actSave": "Ctrl+S",
            "actSaveAs": "Ctrl+Shift+S",
            "actUndo": "Ctrl+Z",
            "actRedo": "Ctrl+Shift+Z",
            "actCut": "Ctrl+X",
            "actCopy": "Ctrl+C",
            "actPaste": "Ctrl+V",
            "actDelete": "Del"
        }
        shortcut_categories = [
            {
                "name": i18n.get("shortcut_category_ui"),
                "shortcuts": [
                    ("shortcut_exit", "actExit"),
                ]
            },
            {
                "name": i18n.get("shortcut_category_file"),
                "shortcuts": [
                    ("shortcut_new", "actNew"),
                    ("shortcut_open", "actOpen"),
                    ("shortcut_save", "actSave"),
                    ("shortcut_saveas", "actSaveAs"),
                ]
            },
            {
                "name": i18n.get("shortcut_category_edit"),
                "shortcuts": [
                    ("shortcut_undo", "actUndo"),
                    ("shortcut_redo", "actRedo"),
                    ("shortcut_cut", "actCut"),
                    ("shortcut_copy", "actCopy"),
                    ("shortcut_paste", "actPaste"),
                    ("shortcut_delete", "actDelete"),
                ]
            },
        ]
        # Kategori başlık ikonları (mutlak yol ile)
        icon_dir = os.path.join(os.path.dirname(__file__), "assets", "icons", "Editor")
        icon_map = {
            i18n.get("shortcut_category_ui"): QIcon(os.path.join(icon_dir, "insert.png")),
            i18n.get("shortcut_category_file"): QIcon(os.path.join(icon_dir, "file.png")),
            i18n.get("shortcut_category_edit"): QIcon(os.path.join(icon_dir, "brush.png")),
        }
        for cat in shortcut_categories:
            cat_item = QTreeWidgetItem([cat["name"]])
            cat_item.setFlags(cat_item.flags() & ~Qt.ItemIsSelectable)
            # Kategori başlığına ikon ekle
            if cat["name"] in icon_map:
                cat_item.setIcon(0, icon_map[cat["name"]])
            self.shortcuts_tree.addTopLevelItem(cat_item)
            for key, action_name in cat["shortcuts"]:
                action = self.actions.get(action_name) if hasattr(self, 'actions') else None
                shortcut = action.shortcut().toString() if action else ""
                item = QTreeWidgetItem([i18n.get(key), shortcut])
                item.setTextAlignment(1, Qt.AlignCenter)
                cat_item.addChild(item)
                self.shortcut_items.append((item, key, action_name))
        self.shortcuts_tree.expandAll()
        self.shortcuts_tree.itemClicked.connect(self.on_shortcut_item_clicked)
        layout.addWidget(self.shortcuts_tree)
        self.reset_btn = QPushButton(i18n.get("reset_defaults_btn"))
        self.reset_btn.clicked.connect(self.reset_shortcuts_to_default)
        self.reset_btn.setObjectName("preferences_reset_btn_objname")
        btn_hbox = QHBoxLayout()
        btn_hbox.addStretch()
        btn_hbox.addWidget(self.reset_btn)
        layout.addLayout(btn_hbox)
        shortcuts_tab.setLayout(layout)
        icon_path = "apps/execution_node_editor/assets/icons/Editor/mixer.png"
        self.tabs.addTab(shortcuts_tab, QIcon(icon_path), i18n.get("menu_shortcuts"))
        self.shortcuts_tab_idx = self.tabs.count() - 1

        self.shortcuts_tree.setStyleSheet("""
QTreeWidget {
    background: #232323;
    color: #e0e0e0;
    border: 1px solid #414141;
    font-size: 13px;
    outline: 0;
}
QTreeWidget::item {
    height: 28px;
    padding: 0 8px;
}
QTreeWidget::item:selected {
    background: #414141;
    color: #ffcc00;
}
QTreeWidget::item:hover {
    background: #333333;
}
QTreeWidget::branch {
    background: transparent;
}
QTreeView::item:has-children {
    font-weight: bold;
    font-size: 15px;
}
QTreeWidget::item:!has-children {
    font-weight: normal;
}
""")
        self.shortcuts_tree.setStyleSheet("""
QTreeWidget {
    font-size: 14px;
}
QTreeWidget::item {
    height: 32px;
    padding: 0 12px;
}
QHeaderView::section {
    background: #232323;
    color: #fff;
    font-weight: bold;
    font-size: 15px;
    height: 36px;
    border: none;
    padding-left: 8px;
}
QTreeWidget::item {
    color: #f0f0f0;
}
""")
        self.shortcuts_tree.header().setSectionResizeMode(0, QHeaderView.Stretch)
        self.shortcuts_tree.header().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.shortcuts_tree.setColumnWidth(0, 350)
        self.shortcuts_tree.setIconSize(QSize(18, 18))
        self.shortcuts_tree.setStyleSheet(self.shortcuts_tree.styleSheet() + """
QTreeWidget::item {
    padding: 0 8px 0 2px;
}
""")

    def on_shortcut_item_clicked(self, item, column=0):
        parent = item.parent()
        if parent is None:
            return
        row = self._find_shortcut_row(item)
        if self._editing_row == row:
            return
        if self._editing_row is not None:
            self.set_recording_icon(self._editing_row, False)
            self.releaseKeyboard()
            self.releaseMouse()
            old_item, key, action_name = self.shortcut_items[self._editing_row]
            action = self.actions.get(action_name) if hasattr(self, 'actions') else None
            shortcut = action.shortcut().toString() if action else self._settings.value(key, "")
            old_item.setText(0, i18n.get(key))
            old_item.setText(1, shortcut)
        self._editing_row = row
        self._recording_row_anim = row
        self._recording_icon_bright = True
        self.set_recording_icon(row, True, QColor(220, 0, 0))
        self._recording_icon_timer.start()
        text = i18n.get('press_new_shortcut')
        if not text:
            text = 'Yeni kısayol için tuşa basın...'
        item.setText(1, f"[{text}]")
        self.shortcuts_tree.setCurrentItem(item)
        self.shortcuts_tree.setFocus()
        self.grabKeyboard()
        self.grabMouse()

    def _find_shortcut_row(self, item):
        for idx, (itm, _, _) in enumerate(self.shortcut_items):
            if itm is item:
                return idx
        return None

    def keyPressEvent(self, event):
        if self._editing_row is not None:
            key_seq = QKeySequence(event.modifiers() | event.key())
            item, key, action_name = self.shortcut_items[self._editing_row]
            item.setText(1, key_seq.toString())
            action = self.actions.get(action_name) if hasattr(self, 'actions') else None
            if action:
                action.setShortcut(key_seq)
            self._settings.setValue(key, key_seq.toString())
            self.set_recording_icon(self._editing_row, False)
            self._recording_icon_timer.stop()
            self._recording_row_anim = None
            self._editing_row = None
            self.releaseKeyboard()
            self.releaseMouse()
            self.apply_btn.setEnabled(True)
        else:
            super().keyPressEvent(event)

    def mousePressEvent(self, event):
        if self._editing_row is not None:
            mouse_map = {
                Qt.LeftButton: 'Mouse Left',
                Qt.RightButton: 'Mouse Right',
                Qt.MiddleButton: 'Mouse Middle',
                Qt.BackButton: 'Mouse Back',
                Qt.ForwardButton: 'Mouse Forward',
            }
            btn = event.button()
            mouse_str = mouse_map.get(btn, f"Mouse {btn}")
            item, key, action_name = self.shortcut_items[self._editing_row]
            item.setText(1, mouse_str)
            self._settings.setValue(key, mouse_str)
            self.set_recording_icon(self._editing_row, False)
            self._recording_icon_timer.stop()
            self._recording_row_anim = None
            self._editing_row = None
            self.releaseKeyboard()
            self.releaseMouse()
            self.apply_btn.setEnabled(True)
        else:
            super().mousePressEvent(event)

    def set_recording_icon(self, row, show, color=None):
        item = self.shortcut_items[row][0]
        if show:
            pix = QPixmap(16, 16)
            pix.fill(Qt.transparent)
            painter = QPainter(pix)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(color if color else QColor(220, 0, 0))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(4, 4, 8, 8)
            painter.end()
            item.setIcon(0, QIcon(pix))
        else:
            item.setIcon(0, QIcon())

    def init_buttons(self):
        btn_layout = QHBoxLayout()
        btn_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        self.apply_btn = QPushButton(i18n.get("apply_btn"))
        self.ok_btn = QPushButton(i18n.get("ok_btn"))
        self.cancel_btn = QPushButton(i18n.get("cancel_btn"))
        self.apply_btn.clicked.connect(self.apply_changes)
        self.ok_btn.clicked.connect(self.ok_clicked)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.setEnabled(False)
        self.apply_btn.setObjectName("preferences_apply_btn_objname")
        self.ok_btn.setObjectName("preferences_ok_btn_objname")
        self.cancel_btn.setObjectName("preferences_cancel_btn_objname")
        btn_layout.addWidget(self.apply_btn)
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        self.layout.addLayout(btn_layout)

    def apply_changes(self):
        if self._pending_lang != i18n.get_language():
            i18n.set_language(self._pending_lang)
        self.apply_btn.setEnabled(False)

    def ok_clicked(self):
        self.apply_changes()
        self.accept()

    def update_ui_texts(self):
        self.setWindowTitle(i18n.get("menu_preferences"))
        self.tabs.setTabText(self.lang_tab_idx, i18n.get("menu_language"))
        self.tabs.setTabText(self.shortcuts_tab_idx, i18n.get("menu_shortcuts"))
        self.lang_combo.setItemText(0, "🇬🇧  " + i18n.get("language_en"))
        self.lang_combo.setItemText(1, "🇹🇷  " + i18n.get("language_tr"))
        self.lang_title.setText(f"<b>{i18n.get('menu_language')}</b>")
        self.lang_desc.setText(i18n.get("language_desc"))
        self.apply_btn.setText(i18n.get("apply_btn"))
        self.ok_btn.setText(i18n.get("ok_btn"))
        self.cancel_btn.setText(i18n.get("cancel_btn"))
        self.reset_btn.setText(i18n.get("reset_defaults_btn"))
        self.shortcuts_tree.setHeaderLabels([i18n.get("menu_shortcuts"), i18n.get("shortcut_key")])
        # Kategori başlıklarını ve ikonlarını güncelle
        icon_dir = os.path.join(os.path.dirname(__file__), "assets", "icons", "Editor")
        icon_map = {
            i18n.get("shortcut_category_ui"): QIcon(os.path.join(icon_dir, "insert.png")),
            i18n.get("shortcut_category_file"): QIcon(os.path.join(icon_dir, "file.png")),
            i18n.get("shortcut_category_edit"): QIcon(os.path.join(icon_dir, "brush.png")),
        }
        for i in range(self.shortcuts_tree.topLevelItemCount()):
            item = self.shortcuts_tree.topLevelItem(i)
            if i == 0:
                item.setText(0, i18n.get("shortcut_category_ui"))
                item.setIcon(0, icon_map[i18n.get("shortcut_category_ui")])
            elif i == 1:
                item.setText(0, i18n.get("shortcut_category_file"))
                item.setIcon(0, icon_map[i18n.get("shortcut_category_file")])
            elif i == 2:
                item.setText(0, i18n.get("shortcut_category_edit"))
                item.setIcon(0, icon_map[i18n.get("shortcut_category_edit")])
        for idx, (item, key, action_name) in enumerate(self.shortcut_items):
            action = self.actions.get(action_name) if hasattr(self, 'actions') else None
            shortcut = action.shortcut().toString() if action else self._settings.value(key, "")
            if self._editing_row == idx:
                text = i18n.get('press_new_shortcut')
                if not text:
                    text = 'Yeni kısayol için tuşa basın...'
                item.setText(1, f"[{text}]")
            else:
                item.setText(0, i18n.get(key))
                item.setText(1, shortcut)
            self.set_recording_icon(idx, False)

    def _toggle_recording_icon(self):
        if self._recording_row_anim is not None:
            self._recording_icon_bright = not self._recording_icon_bright
            color = QColor(220, 0, 0) if self._recording_icon_bright else QColor(120, 0, 0)
            self.set_recording_icon(self._recording_row_anim, True, color)

    def reset_shortcuts_to_default(self):
        for idx, (item, key, action_name) in enumerate(self.shortcut_items):
            default = self.default_shortcuts.get(action_name, "")
            item.setText(1, default)
            if hasattr(self, 'actions') and action_name in self.actions:
                self.actions[action_name].setShortcut(default)
            self._settings.setValue(key, default)
        self.apply_btn.setEnabled(True)
