import os
import glob
import shutil
import argparse
import numpy as np

def sample_frames(source_dir, target_dir, target_count):
    """
    Selects a specified number of frames evenly spaced from a source directory
    and copies them to a target directory.

    Args:
        source_dir (str): Path to the directory containing the original frames (e.g., '1').
        target_dir (str): Path to the directory where sampled frames will be saved (e.g., '1_sampled').
        target_count (int): The desired number of frames to sample (e.g., 16).
    """
    # Validate source directory
    if not os.path.isdir(source_dir):
        pass
        return False, 0

    # Find all frame files - daha genel bir desen kullanalım
    frame_pattern = os.path.join(source_dir, '*.png') # Tüm .png dosyalarını al
    # frame_pattern = os.path.join(source_dir, 'Image Sequence_086_*.png') # Orijinal desen
    frame_files = sorted(glob.glob(frame_pattern))

    if not frame_files:
        pass
        return False, 0

    total_frames = len(frame_files)

    if target_count <= 0:
        pass
        # target_count = 1 # <PERSON><PERSON>a bir var<PERSON><PERSON><PERSON> değere ayarla
        return False, 0
    if target_count > total_frames:
        pass
        target_count = total_frames

    if not os.path.exists(target_dir):
        try:
            os.makedirs(target_dir)
        except OSError as e:
            pass
            return False, 0
    else:
         pass

    indices = np.linspace(0, total_frames - 1, target_count, dtype=int)
    indices = sorted(list(set(indices))) # Benzersizliği ve sıralamayı garantile
    
    print(f"Seçilecek kare indeksleri ({len(indices)} adet): {indices if len(indices) < 20 else str(indices[:10]) + '...' + str(indices[-10:])}") # Çok uzunsa kısalt

    copied_count = 0
    for i, frame_index in enumerate(indices):
        if frame_index < len(frame_files): # Ekstra güvenlik kontrolü
            source_path = frame_files[frame_index]
            target_filename = f"sampled_frame_{i+1:03d}.png" # Sıralı adlandırma
            target_path = os.path.join(target_dir, target_filename)

            try:
                shutil.copy2(source_path, target_path)
                copied_count += 1
            except Exception as e:
                pass
        else:
            pass


    return True, copied_count

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bir klasördeki karelerden belirtilen sayıda örneği seçip başka bir klasöre kopyalar.")
    parser.add_argument("--source", required=True, help="Orijinal kareleri içeren kaynak klasörün yolu.")
    parser.add_argument("--target", required=True, help="Örneklenmiş karelerin kaydedileceği hedef klasörün yolu.")
    parser.add_argument("--count", required=True, type=int, help="Örneklenmesi istenen kare sayısı.")

    args = parser.parse_args()

    sample_frames(args.source, args.target, args.count)
