# -*- coding: utf-8 -*-
"""
Dynamic Node Content Widget - Property'lere göre otomatik UI oluşturan content widget
"""
from typing import TYPE_CHECKING, Dict, List
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, QFrame
from qtpy.QtCore import Qt, Signal, QTimer
from qtpy.QtGui import QFont

from nodeeditor.node_content_widget import QDMNodeContentWidget
from nodeeditor.node_property_system import PropertyWidgetFactory, PropertyWidget
from nodeeditor.node_layout_manager import NodeLayoutManager, LayoutType, LayoutConstraints

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class QDMDynamicContentWidget(QDMNodeContentWidget):
    """Dinamik property'lere göre UI oluşturan content widget"""
    
    # Signals
    contentSizeChanged = Signal()  # İçerik boyutu değ<PERSON>ştiğinde
    propertyWidgetChanged = Signal(str, object)  # Property widget deği<PERSON><PERSON><PERSON><PERSON><PERSON>
    
    def __init__(self, node: 'Node', parent: QWidget = None):
        self._property_widgets: Dict[str, PropertyWidget] = {}
        self._min_width = 200
        self._min_height = 100
        self._max_width = 400
        self._max_height = 600
        self._padding = 8
        self._spacing = 4

        # Layout manager
        self.layout_manager = NodeLayoutManager(node)
        self.use_layout_manager = True

        # Geometry update timer
        self._geometry_timer = QTimer()
        self._geometry_timer.setSingleShot(True)
        self._geometry_timer.timeout.connect(self._update_geometry_delayed)

        super().__init__(node, parent)

        # Property manager'a bağlan
        if hasattr(self.node, 'property_manager'):
            self.node.property_manager.propertyChanged.connect(self.onPropertyChanged)
            self.node.property_manager.propertiesChanged.connect(self.onPropertiesChanged)
    
    def initUI(self):
        """UI'ı başlat"""
        if self.use_layout_manager:
            # Layout manager kullan
            self.setupLayoutManager()
            layout_widget = self.layout_manager.createMainWidget()

            # Ana layout
            self.main_layout = QVBoxLayout()
            self.main_layout.setContentsMargins(0, 0, 0, 0)
            self.main_layout.setSpacing(0)
            self.main_layout.addWidget(layout_widget)
            self.setLayout(self.main_layout)

            # Layout manager'dan widget referanslarını al
            self.property_container = layout_widget
            self._property_widgets = self.layout_manager.property_widgets
        else:
            # Eski sistem kullan
            self.setupLegacyLayout()

        # İlk boyut hesaplaması
        self.updateGeometry()

    def setupLayoutManager(self):
        """Layout manager'ı ayarla"""
        # Constraints ayarla
        constraints = LayoutConstraints()
        constraints.min_width = self._min_width
        constraints.min_height = self._min_height
        constraints.max_width = self._max_width
        constraints.max_height = self._max_height
        constraints.padding.setLeft(self._padding)
        constraints.padding.setTop(self._padding)
        constraints.padding.setRight(self._padding)
        constraints.padding.setBottom(self._padding)
        constraints.spacing = self._spacing

        self.layout_manager.setConstraints(constraints)

        # Property'leri gruplara ayır (örnek)
        self.organizePropertiesIntoGroups()

    def setupLegacyLayout(self):
        """Eski layout sistemini ayarla"""
        # Ana layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(self._padding, self._padding, self._padding, self._padding)
        self.main_layout.setSpacing(self._spacing)
        self.setLayout(self.main_layout)

        # Scroll area oluştur (çok fazla property varsa)
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)

        # Property container widget
        self.property_container = QWidget()
        self.property_layout = QVBoxLayout()
        self.property_layout.setContentsMargins(0, 0, 0, 0)
        self.property_layout.setSpacing(self._spacing)
        self.property_container.setLayout(self.property_layout)

        self.scroll_area.setWidget(self.property_container)
        self.main_layout.addWidget(self.scroll_area)

        # Property widget'larını oluştur
        self.createPropertyWidgets()

    def organizePropertiesIntoGroups(self):
        """Property'leri gruplara ayır"""
        if not hasattr(self.node, 'property_manager'):
            return

        # Temel property'ler için grup
        basic_group = self.layout_manager.addGroup("basic", "Basic Properties")

        # Node property'lerini gruplara ayır
        for prop_name in self.node.property_manager._properties.keys():
            if prop_name.startswith('node_'):
                basic_group.addProperty(prop_name)
            else:
                # Diğer property'ler default gruba
                self.layout_manager.addPropertyToGroup(prop_name, "default")

    def setLayoutType(self, layout_type: LayoutType):
        """Layout türünü ayarla"""
        if self.use_layout_manager:
            self.layout_manager.setLayoutType(layout_type)

    def addPropertyGroup(self, name: str, title: str = None, collapsible: bool = False):
        """Property grubu ekle"""
        if self.use_layout_manager:
            return self.layout_manager.addGroup(name, title, collapsible)
        return None

    def addPropertyToGroup(self, property_name: str, group_name: str = "default"):
        """Property'yi gruba ekle"""
        if self.use_layout_manager:
            self.layout_manager.addPropertyToGroup(property_name, group_name)
    
    def createPropertyWidgets(self):
        """Property'lere göre widget'ları oluştur"""
        if not hasattr(self.node, 'property_manager'):
            return
        
        # Mevcut widget'ları temizle
        self.clearPropertyWidgets()
        
        # Görünür property'ler için widget oluştur
        visible_properties = self.node.property_manager.get_visible_properties()
        
        for prop_name, prop_value in visible_properties.items():
            try:
                # Widget oluştur
                widget = PropertyWidgetFactory.create_widget(prop_value)
                widget.valueChanged.connect(lambda value, name=prop_name: self.onPropertyWidgetChanged(name, value))
                
                # Layout'a ekle
                self.property_layout.addWidget(widget)
                self._property_widgets[prop_name] = widget
                
            except Exception as e:
                print(f"Error creating widget for property {prop_name}: {e}")
        
        # Stretch ekle
        self.property_layout.addStretch()
    
    def clearPropertyWidgets(self):
        """Mevcut property widget'larını temizle"""
        for widget in self._property_widgets.values():
            widget.setParent(None)
            widget.deleteLater()
        self._property_widgets.clear()
        
        # Layout'tan tüm item'ları kaldır
        while self.property_layout.count():
            child = self.property_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
    
    def onPropertyChanged(self, property_name: str, old_value, new_value):
        """Property değiştiğinde widget'ı güncelle"""
        if property_name in self._property_widgets:
            widget = self._property_widgets[property_name]
            widget.updateValue()
        
        # Boyut güncellemesi gerekebilir
        self.scheduleGeometryUpdate()
    
    def onPropertiesChanged(self):
        """Property'ler değiştiğinde tüm UI'ı yeniden oluştur"""
        self.createPropertyWidgets()
        self.scheduleGeometryUpdate()
    
    def onPropertyWidgetChanged(self, property_name: str, value):
        """Property widget değiştiğinde"""
        self.propertyWidgetChanged.emit(property_name, value)
        self.scheduleGeometryUpdate()
    
    def scheduleGeometryUpdate(self):
        """Geometry güncellemesini planla (debounce için)"""
        self._geometry_timer.start(50)  # 50ms delay
    
    def _update_geometry_delayed(self):
        """Geciktirilmiş geometry güncellemesi"""
        self.updateGeometry()
        self.contentSizeChanged.emit()
    
    def updateGeometry(self):
        """Widget boyutunu güncelle"""
        if not self.property_container:
            return
        
        # İçerik boyutunu hesapla
        content_height = self.calculateContentHeight()
        content_width = self.calculateContentWidth()
        
        # Minimum/maksimum sınırları uygula
        final_width = max(self._min_width, min(content_width, self._max_width))
        final_height = max(self._min_height, min(content_height, self._max_height))
        
        # Boyutu ayarla
        self.setFixedSize(final_width, final_height)
        
        # Scroll area'nın boyutunu ayarla
        scroll_height = final_height - 2 * self._padding
        self.scroll_area.setFixedHeight(scroll_height)
        
        # Parent node'a boyut değişikliğini bildir
        if hasattr(self.node, 'grNode') and self.node.grNode:
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
    
    def calculateContentHeight(self) -> int:
        """İçerik yüksekliğini hesapla"""
        if not self._property_widgets:
            return self._min_height
        
        total_height = 2 * self._padding  # Top and bottom padding
        
        # Her widget'ın yüksekliğini topla
        for widget in self._property_widgets.values():
            widget_height = widget.sizeHint().height()
            total_height += widget_height + self._spacing
        
        # Son spacing'i çıkar
        if self._property_widgets:
            total_height -= self._spacing
        
        return total_height
    
    def calculateContentWidth(self) -> int:
        """İçerik genişliğini hesapla"""
        if not self._property_widgets:
            return self._min_width
        
        max_width = 0
        
        # En geniş widget'ı bul
        for widget in self._property_widgets.values():
            widget_width = widget.sizeHint().width()
            max_width = max(max_width, widget_width)
        
        # Padding ekle
        total_width = max_width + 2 * self._padding
        
        return total_width
    
    def setMinimumSize(self, width: int, height: int):
        """Minimum boyutu ayarla"""
        self._min_width = width
        self._min_height = height
        self.updateGeometry()
    
    def setMaximumSize(self, width: int, height: int):
        """Maksimum boyutu ayarla"""
        self._max_width = width
        self._max_height = height
        self.updateGeometry()
    
    def setPadding(self, padding: int):
        """Padding'i ayarla"""
        self._padding = padding
        self.main_layout.setContentsMargins(padding, padding, padding, padding)
        self.updateGeometry()
    
    def setSpacing(self, spacing: int):
        """Spacing'i ayarla"""
        self._spacing = spacing
        self.main_layout.setSpacing(spacing)
        self.property_layout.setSpacing(spacing)
        self.updateGeometry()
    
    def getPropertyWidget(self, property_name: str) -> PropertyWidget:
        """Belirli bir property'nin widget'ını al"""
        return self._property_widgets.get(property_name)
    
    def getAllPropertyWidgets(self) -> Dict[str, PropertyWidget]:
        """Tüm property widget'larını al"""
        return self._property_widgets.copy()
    
    def serialize(self) -> dict:
        """Content widget'ı serialize et"""
        data = super().serialize() if hasattr(super(), 'serialize') else {}
        data.update({
            'min_width': self._min_width,
            'min_height': self._min_height,
            'max_width': self._max_width,
            'max_height': self._max_height,
            'padding': self._padding,
            'spacing': self._spacing
        })
        return data
    
    def deserialize(self, data: dict, hashmap: dict = {}, restore_id: bool = True):
        """Content widget'ı deserialize et"""
        if hasattr(super(), 'deserialize'):
            super().deserialize(data, hashmap, restore_id)
        
        # Boyut ayarlarını geri yükle
        self._min_width = data.get('min_width', self._min_width)
        self._min_height = data.get('min_height', self._min_height)
        self._max_width = data.get('max_width', self._max_width)
        self._max_height = data.get('max_height', self._max_height)
        self._padding = data.get('padding', self._padding)
        self._spacing = data.get('spacing', self._spacing)
        
        # UI'ı yeniden oluştur
        self.createPropertyWidgets()
        self.updateGeometry()
        
        return True
