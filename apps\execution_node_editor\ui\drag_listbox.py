from PyQt5.QtGui import QBrush, QColor, QFont
from apps.execution_node_editor.conf import LISTBOX_MIMETYPE, nodeTypes
from qtpy.QtGui import QPixmap, QIcon, QDrag
from qtpy.QtCore import QSize, Qt, QByteArray, QDataStream, QMimeData, QIODevice, QPoint
from qtpy.QtWidgets import QTreeWidget, QAbstractItemView, QTreeWidgetItem 
from apps.execution_node_editor.localization import i18n

from nodeeditor.utils import dumpException

class QDMDragListbox(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        i18n.languageChanged.connect(self.updateTexts)

    def initUI(self):
        # init
        self.setIconSize(QSize(32, 32))
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setDragEnabled(True)
        self.setColumnCount(1)
        self.updateTexts()
        self._font = QFont("Roboto", 12)
        self.setFont(self._font)

    def updateTexts(self):
        # Debug kodu kaldırıldı
        self.setHeaderLabels([i18n.get("nodes_list_header")])
        self.clear()
        self.addMyItems()

    def addMyItems(self):
        # Debug kodu kaldırıldı
        items = []
        for key, values in nodeTypes.items():
            # System kategorisini gizle
            if key == "system":
                continue

            item = QTreeWidgetItem([i18n.get(key)])
            color = QColor("#a0a9b8")
            brush = QBrush(color)
            item.setForeground(0, brush)
            for value in values:
                child = QTreeWidgetItem([i18n.get(value)])
                child.setForeground(0, brush)
                child.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsDragEnabled)
                # setup data - gerçek node_type'ı sakla
                child.setData(0, Qt.UserRole + 1, value)  # value = gerçek node_type (örn: "FrameSampler")
                item.addChild(child)
            items.append(item)
        
        self.insertTopLevelItems(0, items)
        #keys = list(CALC_NODES.keys())
        #keys.sort()
        #for key in keys:
        #    node = get_class_from_opcode(key)
        #    self.addMyItem(node.op_title, node.icon, node.op_code)

    def show(self):
        # Debug kodu kaldırıldı
        super().show()

    def startDrag(self, *args, **kwargs):
        # Debug kodu kaldırıldı
        try:
            # Debug kodu kaldırıldı
            item = self.currentItem()
            node_type = item.data(0, Qt.UserRole + 1)  # Gerçek node_type'ı al

            pixmap = QPixmap(".")

            itemData = QByteArray()
            dataStream = QDataStream(itemData, QIODevice.WriteOnly)
            dataStream << pixmap
            dataStream.writeQString(node_type)  # node_type'ı gönder (Türkçe isim değil)
            dataStream.writeQString(item.text(0))  # Display name'i de gönder

            mimeData = QMimeData()
            mimeData.setData(LISTBOX_MIMETYPE, itemData)

            drag = QDrag(self)
            drag.setMimeData(mimeData)
            drag.setHotSpot(QPoint(int(pixmap.width() / 2), int(pixmap.height() / 2)))
            drag.setPixmap(pixmap)

            drag.exec_(Qt.MoveAction)

        except Exception as e: dumpException(e)