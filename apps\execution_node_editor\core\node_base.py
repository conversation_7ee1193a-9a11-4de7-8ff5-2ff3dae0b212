from qtpy.QtGui import QImage
from qtpy.QtCore import QRectF
from qtpy.QtWidgets import <PERSON><PERSON>abel

from nodeeditor.node_node import Node
from nodeeditor.node_content_widget import QDMNodeContentWidget
from nodeeditor.node_graphics_node import QDMGraphicsNode
from nodeeditor.node_socket import LEFT_CENTER, RIGHT_CENTER
from nodeeditor.utils import dumpException


class CalcGraphicsNode(QDMGraphicsNode):
    def initSizes(self):
        super().initSizes()
        self.width = 160
        self.height = 74
        self.edge_roundness = 6
        self.edge_padding = 0
        self.title_horizontal_padding = 8
        self.title_vertical_padding = 10

    def initAssets(self):
        super().initAssets()

    def paint(self, painter, QStyleOptionGraphicsItem, widget=None):
        super().paint(painter, QStyleOptionGraphicsItem, widget)

        offset = 24.0
        if self.node.isDirty(): offset = 0.0
        if self.node.isInvalid(): offset = 48.0

class CalcContent(QDMNodeContentWidget):
    def initUI(self):
        from qtpy.QtWidgets import QVBoxLayout
        from qtpy.QtCore import Qt

        # Ana layout oluştur
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(4, 4, 4, 4)
        self.layout.setSpacing(2)
        self.setLayout(self.layout)

        # Label oluştur
        if hasattr(self.node, 'content_label') and self.node.content_label:
            self.lbl = QLabel(self.node.content_label, self)
            self.lbl.setObjectName(getattr(self.node, 'content_label_objname', 'calc_node_label'))
            self.lbl.setAlignment(Qt.AlignCenter)
            self.lbl.setWordWrap(True)
            self.layout.addWidget(self.lbl)
        else:
            # Boş label oluştur (uyumluluk için)
            self.lbl = QLabel("", self)
            self.lbl.setObjectName('calc_node_label')
            self.lbl.setAlignment(Qt.AlignCenter)
            self.layout.addWidget(self.lbl)

        # Uyumluluk için wdg_label alias'ı
        self.wdg_label = self.lbl

        # Minimum boyut ayarla
        self.setMinimumSize(120, 30)

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        if hasattr(self, 'lbl') and self.lbl.text():
            # Label'ın boyutuna göre hesapla
            label_size = self.lbl.sizeHint()
            width = max(120, label_size.width() + 8)  # Padding ekle
            height = max(30, label_size.height() + 8)
            return QSize(width, height)
        else:
            return QSize(120, 30)  # Varsayılan boyut


class CalcNode(Node):
    icon = ""
    op_code = 0
    content_label = ""
    content_label_objname = "calc_node_bg"

    GraphicsNode_class = CalcGraphicsNode
    NodeContent_class = CalcContent

    def __init__(self, scene, inputs=[2,2], outputs=[1]):
        super().__init__(scene, self.__class__.op_title, inputs, outputs)

        self.value = None

        # it's really important to mark all nodes Dirty by default
        self.markDirty()


    def initSettings(self):
        super().initSettings()
        self.input_socket_position = LEFT_CENTER
        self.output_socket_position = RIGHT_CENTER

    def evalOperation(self, input1, input2):
        return 123

    def evalImplementation(self):
        i1 = self.getInput(0)
        i2 = self.getInput(1)

        if i1 is None or i2 is None:
            self.markInvalid()
            self.markDescendantsDirty()
            self.grNode.setToolTip("Connect all inputs")
            return None

        else:
            val = self.evalOperation(i1.eval(), i2.eval())
            self.value = val
            self.markDirty(False)
            self.markInvalid(False)
            self.grNode.setToolTip("")

            self.markDescendantsDirty()
            self.evalChildren()

            return val

    def eval(self):
        if not self.isDirty() and not self.isInvalid():
            return self.value

        try:

            val = self.evalImplementation()
            return val
        except ValueError as e:
            self.markInvalid()
            self.grNode.setToolTip(str(e))
            self.markDescendantsDirty()
        except Exception as e:
            self.markInvalid()
            self.grNode.setToolTip(str(e))
            dumpException(e)



    def onInputChanged(self, socket=None):
        self.markDirty()
        self.eval()


    def serialize(self):
        res = super().serialize()
        res['op_code'] = self.__class__.op_code
        return res

    def deserialize(self, data, hashmap={}, restore_id=True):
        res = super().deserialize(data, hashmap, restore_id)
        return res