"""
Scene selection management için ayrı modül
"""
from PyQt5.QtCore import QObject, pyqtSignal
from typing import List, Callable


class SceneSelectionManager(QObject):
    """Scene'deki seçim işlemlerini yöneten sınıf"""
    
    # Sinyaller
    item_selected = pyqtSignal()
    items_deselected = pyqtSignal()
    selection_changed = pyqtSignal(list)  # Seçili öğeler listesi
    
    def __init__(self, scene, parent=None):
        super().__init__(parent)
        self.scene = scene
        self._silent_selection_events = False
        self._last_selected_items = None
        
        # Event listeners
        self._item_selected_listeners = []
        self._items_deselected_listeners = []
        
        # Graphics scene sinyallerini bağla
        if hasattr(scene, 'grScene'):
            scene.grScene.itemSelected.connect(self.on_item_selected)
            scene.grScene.itemsDeselected.connect(self.on_items_deselected)
    
    def set_silent_selection_events(self, value: bool = True):
        """<PERSON><PERSON><PERSON>ını sessiz hale getirir"""
        self._silent_selection_events = value
    
    def get_selected_items(self) -> List:
        """Seçili öğeleri döndürür"""
        if hasattr(self.scene, 'grScene'):
            return self.scene.grScene.selectedItems()
        return []
    
    def deselect_all_items(self, silent: bool = False):
        """Tüm öğelerin seçimini kaldırır"""
        for item in self.get_selected_items():
            item.setSelected(False)
        if not silent:
            self.on_items_deselected()
    
    def on_item_selected(self, silent: bool = False):
        """Öğe seçildiğinde çağrılır"""
        if self._silent_selection_events:
            return
        
        current_selected_items = self.get_selected_items()
        if current_selected_items != self._last_selected_items:
            self._last_selected_items = current_selected_items
            
            # Sinyalleri gönder
            self.item_selected.emit()
            self.selection_changed.emit(current_selected_items)
            
            if not silent:
                # Callback'leri çağır
                for callback in self._item_selected_listeners:
                    callback()
                
                # History'ye kaydet
                if hasattr(self.scene, 'history'):
                    self.scene.history.storeHistory("Selection Changed")
    
    def on_items_deselected(self, silent: bool = False):
        """Öğelerin seçimi kaldırıldığında çağrılır"""
        # Seçimin gerçekten değişip değişmediğini kontrol et
        current_selected_items = self.get_selected_items()
        if current_selected_items == self._last_selected_items:
            return
        
        self.reset_last_selected_states()
        
        if current_selected_items == []:
            self._last_selected_items = []
            
            # Sinyalleri gönder
            self.items_deselected.emit()
            self.selection_changed.emit([])
            
            if not silent:
                # History'ye kaydet
                if hasattr(self.scene, 'history'):
                    self.scene.history.storeHistory("Deselected Everything")
                
                # Callback'leri çağır
                for callback in self._items_deselected_listeners:
                    callback()
    
    def reset_last_selected_states(self):
        """Tüm node ve edge'lerin seçim durumlarını sıfırlar"""
        if hasattr(self.scene, 'nodes'):
            for node in self.scene.nodes:
                if hasattr(node, 'grNode'):
                    node.grNode._last_selected_state = False
        
        if hasattr(self.scene, 'edges'):
            for edge in self.scene.edges:
                if hasattr(edge, 'grEdge'):
                    edge.grEdge._last_selected_state = False
    
    def add_item_selected_listener(self, callback: Callable):
        """Öğe seçimi listener'ı ekler"""
        self._item_selected_listeners.append(callback)
    
    def add_items_deselected_listener(self, callback: Callable):
        """Öğe seçim kaldırma listener'ı ekler"""
        self._items_deselected_listeners.append(callback)
    
    def remove_item_selected_listener(self, callback: Callable):
        """Öğe seçimi listener'ını kaldırır"""
        if callback in self._item_selected_listeners:
            self._item_selected_listeners.remove(callback)
    
    def remove_items_deselected_listener(self, callback: Callable):
        """Öğe seçim kaldırma listener'ını kaldırır"""
        if callback in self._items_deselected_listeners:
            self._items_deselected_listeners.remove(callback)
    
    def get_selection_info(self):
        """Seçim bilgilerini döndürür"""
        selected_items = self.get_selected_items()
        return {
            'count': len(selected_items),
            'items': selected_items,
            'last_selected': self._last_selected_items,
            'silent_mode': self._silent_selection_events
        }
    
    def cleanup(self):
        """Kaynakları temizler"""
        self._item_selected_listeners.clear()
        self._items_deselected_listeners.clear()
        self._last_selected_items = None
