from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt
from .base_thumbnailer import BaseThumbnailer
import os

class ImageThumbnailer(BaseThumbnailer):
    """
    Tek<PERSON> resim dosyaları için özelleştirilmiş thumbnailer sınıfı.
    Resim dosyalarını asenkron olarak yükler ve önizleme oluşturur.
    """
    def __init__(self, scene=None, parent=None, parent_node=None):
        super().__init__(scene, parent, parent_node)
        self.title = "Image Preview"
        self.image_path = None
    
    def load_content(self, image_path):
        """
        Resim dosyasını asenkron olarak yükler ve önizleme oluşturur
        """
        if not image_path or not os.path.exists(image_path):
            self.title = "No Image"
            self.pixmap = None
            self.set_visible(False)
            return
        
        self.image_path = image_path
        self.title = os.path.basename(image_path)
        
        # Asenkron olarak pixmap oluştur
        self.process_pixmap_async(self._load_image, image_path)
        self.set_visible(True)
        self.update_position()
    
    def _load_image(self, image_path):
        """
        Resim dosyasını yükler ve QPixmap döndürür.
        Bu metod worker thread üzerinde çalışır.
        """
        try:
            # Burada ağır işlemler yapılabilir, ana thread'i bloke etmez
            pixmap = QPixmap(image_path)
            
            # Eğer pixmap geçersizse None döndür
            if pixmap.isNull():
                return None
            
            # Kaliteyi düşür (3/2 oranında)
            reduced_pixmap = self.reduce_image_quality(pixmap)
                
            return reduced_pixmap
        except Exception as e:
            return None
    
    def refresh(self):
        """
        Önizlemeyi yeniler
        """
        if self.image_path:
            self.load_content(self.image_path)
