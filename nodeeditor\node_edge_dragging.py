# -*- coding: utf-8 -*-
"""
A module containing the Edge Dragging functionality
"""
from nodeeditor.node_graphics_socket import QDMGraphicsSocket
from nodeeditor.node_edge import EDGE_TYPE_DEFAULT
from nodeeditor.utils import dumpException
from qtpy.QtWidgets import QGraphicsView, QGraphicsItem


class EdgeDragging:
    def __init__(self, grView:'QGraphicsView'):
        self.grView = grView
        self.drag_edge = None
        self.drag_start_socket = None

    def getEdgeClass(self):
        return self.grView.grScene.scene.getEdgeClass()

    def updateDestination(self, x: float, y: float):
        if self.drag_edge is not None:
            if self.drag_edge.grEdge is not None:
                self.drag_edge.grEdge.setDestination(x, y)
                self.drag_edge.grEdge.update()

    def edgeDragStart(self, item:'QGraphicsItem'):
        try:
            self.drag_start_socket = item.socket
            self.drag_edge = self.getEdgeClass()(item.socket.node.scene, item.socket, None, EDGE_TYPE_DEFAULT)
            if self.drag_edge and self.drag_edge.grEdge:
                self.drag_edge.grEdge.makeUnselectable()
        except Exception as e:
            dumpException(e)

    def edgeDragEnd(self, item:'QGraphicsItem'):
        if not isinstance(item, QDMGraphicsSocket):
            self.grView.resetMode()
            if self.drag_edge:
                self.drag_edge.remove(silent=True)
            self.drag_edge = None

        if isinstance(item, QDMGraphicsSocket):
            if not self.drag_edge:
                return False
            if not self.drag_edge.validateEdge(self.drag_start_socket, item.socket):
                return False
            self.grView.resetMode()
            self.drag_edge.remove(silent=True)
            self.drag_edge = None
            try:
                if item.socket != self.drag_start_socket:
                    for socket in (item.socket, self.drag_start_socket):
                        if not socket.is_multi_edges:
                            if socket.is_input:
                                socket.removeAllEdges(silent=True)
                            else:
                                socket.removeAllEdges(silent=False)
                    new_edge = self.getEdgeClass()(item.socket.node.scene, self.drag_start_socket, item.socket, edge_type=EDGE_TYPE_DEFAULT)
                    for socket in [self.drag_start_socket, item.socket]:
                        socket.node.onEdgeConnectionChanged(new_edge)
                        if socket.is_input: socket.node.onInputChanged(socket)
                    self.grView.grScene.scene.history.storeHistory("Created new edge by dragging", setModified=True)
                    return True
            except Exception as e:
                dumpException(e)
        return False

