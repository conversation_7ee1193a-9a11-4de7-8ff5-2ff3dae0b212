# -*- coding: utf-8 -*-
"""
Node Compatibility Layer - Mevcut node'ları etkilemeden yeni sistemi entegre etmek için
"""
from typing import TYPE_CHECKING, Optional, Any, Dict, List
from qtpy.QtCore import QObject

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class NodeCompatibilityManager:
    """Node uyumluluk yöneticisi"""
    
    def __init__(self):
        self._legacy_nodes: Dict[str, type] = {}
        self._dynamic_nodes: Dict[str, type] = {}
        self._compatibility_mode = True
    
    def register_legacy_node(self, node_type: str, node_class: type):
        """Eski node sınıfını kaydet"""
        self._legacy_nodes[node_type] = node_class
    
    def register_dynamic_node(self, node_type: str, node_class: type):
        """Yeni dinamik node sınıfını kaydet"""
        self._dynamic_nodes[node_type] = node_class
    
    def is_legacy_node(self, node_type: str) -> bool:
        """Node'un eski sistem mi olduğunu kontrol et"""
        return node_type in self._legacy_nodes
    
    def is_dynamic_node(self, node_type: str) -> bool:
        """Node'un yeni sistem mi olduğunu kontrol et"""
        return node_type in self._dynamic_nodes
    
    def get_node_class(self, node_type: str) -> Optional[type]:
        """Node türüne göre sınıfı al"""
        if node_type in self._dynamic_nodes:
            return self._dynamic_nodes[node_type]
        elif node_type in self._legacy_nodes:
            return self._legacy_nodes[node_type]
        return None
    
    def set_compatibility_mode(self, enabled: bool):
        """Uyumluluk modunu aç/kapat"""
        self._compatibility_mode = enabled
    
    def is_compatibility_mode(self) -> bool:
        """Uyumluluk modu aktif mi"""
        return self._compatibility_mode


class LegacyNodeWrapper:
    """Eski node'ları yeni sistem ile uyumlu hale getiren wrapper"""
    
    def __init__(self, node: 'Node'):
        self.node = node
        self._wrapped_methods = {}
        self._setup_compatibility()
    
    def _setup_compatibility(self):
        """Uyumluluk katmanını ayarla"""
        # Yeni sistemin metodlarını kontrol et ve gerekirse wrapper ekle
        if not hasattr(self.node, 'property_manager'):
            self._add_property_manager_compatibility()
        
        if not hasattr(self.node, 'socket_manager'):
            self._add_socket_manager_compatibility()
        
        if not hasattr(self.node, 'geometry_manager'):
            self._add_geometry_manager_compatibility()
    
    def _add_property_manager_compatibility(self):
        """Property manager uyumluluğu ekle"""
        # Basit property manager mock'u
        class MockPropertyManager:
            def __init__(self):
                self._properties = {}
            
            def get_property(self, name):
                return getattr(self.node, name, None) if hasattr(self.node, name) else None
            
            def set_property(self, name, value):
                if hasattr(self.node, name):
                    setattr(self.node, name, value)
            
            def serialize(self):
                return {}
            
            def deserialize(self, data, hashmap={}, restore_id=True):
                return True
        
        self.node.property_manager = MockPropertyManager()
        
        # Property metodları ekle
        self.node.getProperty = self.node.property_manager.get_property
        self.node.setProperty = self.node.property_manager.set_property
    
    def _add_socket_manager_compatibility(self):
        """Socket manager uyumluluğu ekle"""
        # Basit socket manager mock'u
        class MockSocketManager:
            def __init__(self, node):
                self.node = node
            
            def get_socket_by_name(self, name):
                # Mevcut socket'larda ara
                for socket in self.node.inputs + self.node.outputs:
                    if hasattr(socket, 'socket_name') and socket.socket_name == name:
                        return socket
                return None
            
            def add_socket(self, definition):
                # Eski sistem ile uyumlu socket ekleme
                return None
            
            def remove_socket(self, socket_id):
                # Eski sistem ile uyumlu socket kaldırma
                return False
        
        self.node.socket_manager = MockSocketManager(self.node)
        
        # Socket metodları ekle
        self.node.getSocketByName = self.node.socket_manager.get_socket_by_name
        self.node.addInputSocket = lambda name, socket_type=None, label=None, required=False: None
        self.node.addOutputSocket = lambda name, socket_type=None, label=None: None
        self.node.removeSocket = self.node.socket_manager.remove_socket
    
    def _add_geometry_manager_compatibility(self):
        """Geometry manager uyumluluğu ekle"""
        from nodeeditor.node_geometry_observer import getGeometryManager
        self.node.geometry_manager = getGeometryManager()


class NodeMigrationHelper:
    """Node'ları eski sistemden yeni sisteme geçirmek için yardımcı"""
    
    @staticmethod
    def migrate_node_to_dynamic(legacy_node: 'Node') -> 'Node':
        """Eski node'u dinamik sisteme geçir"""
        # Bu fonksiyon gelecekte implement edilecek
        # Şimdilik eski node'u wrapper ile uyumlu hale getir
        wrapper = LegacyNodeWrapper(legacy_node)
        return legacy_node
    
    @staticmethod
    def extract_properties_from_legacy(legacy_node: 'Node') -> Dict[str, Any]:
        """Eski node'dan property'leri çıkar"""
        properties = {}
        
        # Node'un attribute'larını tara
        for attr_name in dir(legacy_node):
            if not attr_name.startswith('_') and not callable(getattr(legacy_node, attr_name)):
                attr_value = getattr(legacy_node, attr_name)
                if isinstance(attr_value, (str, int, float, bool)):
                    properties[attr_name] = attr_value
        
        return properties
    
    @staticmethod
    def extract_sockets_from_legacy(legacy_node: 'Node') -> Dict[str, List]:
        """Eski node'dan socket'ları çıkar"""
        sockets = {
            'inputs': [],
            'outputs': []
        }
        
        # Input socket'ları
        for socket in legacy_node.inputs:
            socket_info = {
                'name': getattr(socket, 'socket_name', f'input_{socket.index}'),
                'type': getattr(socket, 'socket_type', 'null'),
                'position': socket.position,
                'index': socket.index
            }
            sockets['inputs'].append(socket_info)
        
        # Output socket'ları
        for socket in legacy_node.outputs:
            socket_info = {
                'name': getattr(socket, 'socket_name', f'output_{socket.index}'),
                'type': getattr(socket, 'socket_type', 'null'),
                'position': socket.position,
                'index': socket.index
            }
            sockets['outputs'].append(socket_info)
        
        return sockets


class CompatibilityChecker:
    """Uyumluluk kontrolü yapan sınıf"""
    
    @staticmethod
    def check_node_compatibility(node: 'Node') -> Dict[str, bool]:
        """Node'un yeni sistem ile uyumluluğunu kontrol et"""
        compatibility = {
            'has_property_manager': hasattr(node, 'property_manager'),
            'has_socket_manager': hasattr(node, 'socket_manager'),
            'has_geometry_manager': hasattr(node, 'geometry_manager'),
            'has_dynamic_content': hasattr(node, 'content') and hasattr(node.content, 'layout_manager'),
            'is_legacy': not (hasattr(node, 'property_manager') and hasattr(node, 'socket_manager')),
            'needs_wrapper': False
        }
        
        # Wrapper gerekli mi kontrol et
        compatibility['needs_wrapper'] = compatibility['is_legacy']
        
        return compatibility
    
    @staticmethod
    def get_compatibility_report(nodes: List['Node']) -> Dict[str, Any]:
        """Node listesi için uyumluluk raporu oluştur"""
        report = {
            'total_nodes': len(nodes),
            'legacy_nodes': 0,
            'dynamic_nodes': 0,
            'wrapped_nodes': 0,
            'compatibility_issues': []
        }
        
        for node in nodes:
            compatibility = CompatibilityChecker.check_node_compatibility(node)
            
            if compatibility['is_legacy']:
                report['legacy_nodes'] += 1
                if compatibility['needs_wrapper']:
                    report['wrapped_nodes'] += 1
            else:
                report['dynamic_nodes'] += 1
            
            # Uyumluluk sorunlarını topla
            issues = []
            if not compatibility['has_property_manager']:
                issues.append('Missing property manager')
            if not compatibility['has_socket_manager']:
                issues.append('Missing socket manager')
            if not compatibility['has_geometry_manager']:
                issues.append('Missing geometry manager')
            
            if issues:
                report['compatibility_issues'].append({
                    'node': str(node),
                    'issues': issues
                })
        
        return report


# Global compatibility manager
_compatibility_manager = None

def getCompatibilityManager() -> NodeCompatibilityManager:
    """Global uyumluluk yöneticisini al"""
    global _compatibility_manager
    if _compatibility_manager is None:
        _compatibility_manager = NodeCompatibilityManager()
    return _compatibility_manager


def ensure_node_compatibility(node: 'Node') -> 'Node':
    """Node'un yeni sistem ile uyumlu olduğundan emin ol"""
    compatibility = CompatibilityChecker.check_node_compatibility(node)
    
    if compatibility['needs_wrapper']:
        wrapper = LegacyNodeWrapper(node)
        return node
    
    return node


def migrate_legacy_nodes(nodes: List['Node']) -> List['Node']:
    """Eski node'ları yeni sisteme geçir"""
    migrated_nodes = []
    
    for node in nodes:
        migrated_node = ensure_node_compatibility(node)
        migrated_nodes.append(migrated_node)
    
    return migrated_nodes
