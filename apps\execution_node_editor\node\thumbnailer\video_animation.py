"""
Video animasyon ve frame geçişleri için ayrı modül
"""
from PyQt5.QtCore import QTimer, QObject, pyqtSignal


class VideoAnimationManager(QObject):
    """Video frame animasyonlarını yöneten sınıf"""
    
    # Sinyaller
    frame_changed = pyqtSignal(int)  # Yeni frame indeksi
    animation_started = pyqtSignal()
    animation_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.frame_timer = QTimer()
        self.frame_timer.timeout.connect(self._on_timer_timeout)
        
        # Animasyon ayarları
        self.frame_count = 0
        self.current_frame_index = 0
        self.fps = 30
        self.preview_mode = "animated"  # "animated" veya "static"
        self.is_playing = False
        
        # Timer ayarları
        self.animation_fps = 2  # Önizleme için 2 FPS
        self._update_timer_interval()
    
    def set_video_info(self, frame_count, fps):
        """Video bilgilerini ayarla"""
        self.frame_count = frame_count
        self.fps = fps
        self.current_frame_index = 0
    
    def set_preview_mode(self, mode):
        """Önizleme modunu ayarla"""
        if mode not in ["animated", "static"]:
            return
        
        old_mode = self.preview_mode
        self.preview_mode = mode
        
        if old_mode != mode:
            if mode == "animated" and self.is_playing:
                self.start_animation()
            elif mode == "static":
                self.stop_animation()
                # Statik modda ilk frame'i göster
                self.current_frame_index = 0
                self.frame_changed.emit(0)
    
    def start_animation(self):
        """Animasyonu başlat"""
        if self.preview_mode == "animated" and self.frame_count > 1:
            self.is_playing = True
            self.frame_timer.start()
            self.animation_started.emit()
    
    def stop_animation(self):
        """Animasyonu durdur"""
        self.is_playing = False
        self.frame_timer.stop()
        self.animation_stopped.emit()
    
    def toggle_animation(self):
        """Animasyonu başlat/durdur"""
        if self.is_playing:
            self.stop_animation()
        else:
            self.start_animation()
        return self.is_playing
    
    def seek_to_position(self, position):
        """Belirli pozisyona git (0.0 - 1.0)"""
        if self.frame_count <= 1:
            return
        
        # Animasyonu geçici olarak durdur
        was_playing = self.is_playing
        if was_playing:
            self.stop_animation()
        
        # Yeni frame indeksini hesapla
        frame_index = int(position * (self.frame_count - 1))
        frame_index = max(0, min(frame_index, self.frame_count - 1))
        
        self.current_frame_index = frame_index
        self.frame_changed.emit(frame_index)
        
        # Eğer oynatılıyorsa, animasyonu yeniden başlat
        if was_playing:
            self.start_animation()
    
    def seek_to_frame(self, frame_index):
        """Belirli frame'e git"""
        if 0 <= frame_index < self.frame_count:
            self.current_frame_index = frame_index
            self.frame_changed.emit(frame_index)
    
    def next_frame(self):
        """Sonraki frame'e geç"""
        if self.frame_count > 1:
            self.current_frame_index = (self.current_frame_index + 1) % self.frame_count
            self.frame_changed.emit(self.current_frame_index)
    
    def previous_frame(self):
        """Önceki frame'e geç"""
        if self.frame_count > 1:
            self.current_frame_index = (self.current_frame_index - 1) % self.frame_count
            self.frame_changed.emit(self.current_frame_index)
    
    def get_current_position(self):
        """Mevcut pozisyonu döndür (0.0 - 1.0)"""
        if self.frame_count <= 1:
            return 0.0
        return self.current_frame_index / (self.frame_count - 1)
    
    def get_current_frame(self):
        """Mevcut frame indeksini döndür"""
        return self.current_frame_index
    
    def set_animation_fps(self, fps):
        """Animasyon FPS'ini ayarla"""
        self.animation_fps = max(0.1, min(fps, 30))  # 0.1 - 30 FPS arası
        self._update_timer_interval()
    
    def _update_timer_interval(self):
        """Timer aralığını güncelle"""
        interval = int(1000 / self.animation_fps)  # ms cinsinden
        self.frame_timer.setInterval(interval)
    
    def _on_timer_timeout(self):
        """Timer timeout olduğunda çağrılır"""
        self.next_frame()
    
    def reset(self):
        """Animasyon manager'ını sıfırla"""
        self.stop_animation()
        self.frame_count = 0
        self.current_frame_index = 0
        self.fps = 30
    
    def cleanup(self):
        """Kaynakları temizle"""
        self.stop_animation()
        self.frame_timer.deleteLater()
    
    def get_animation_info(self):
        """Animasyon bilgilerini döndür"""
        return {
            'frame_count': self.frame_count,
            'current_frame': self.current_frame_index,
            'fps': self.fps,
            'animation_fps': self.animation_fps,
            'preview_mode': self.preview_mode,
            'is_playing': self.is_playing,
            'position': self.get_current_position()
        }
