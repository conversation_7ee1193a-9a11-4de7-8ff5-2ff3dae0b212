# -*- coding: utf-8 -*-
"""
A module containing the Edge Validator functions which can be registered as callbacks to
:class:`~nodeeditor.node_edge.Edge` class.

Example of registering Edge Validator callbacks:
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

You can register validation callbacks once for example on the bottom of node_edge.py file or on the
application start with calling this:

.. code-block:: python

    from nodeeditor.node_edge_validators import *

    Edge.registerEdgeValidator(edge_validator_debug)
    Edge.registerEdgeValidator(edge_cannot_connect_two_outputs_or_two_inputs)
    Edge.registerEdgeValidator(edge_cannot_connect_input_and_output_of_same_node)
    Edge.registerEdgeValidator(edge_cannot_connect_input_and_output_of_different_type)


"""

from apps.execution_node_editor.core.data_types import DataType
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from nodeeditor.node_socket import Socket

# <PERSON><PERSON><PERSON><PERSON> tipler haritası (gerekirse genişletilebilir)
COMPATIBLE_TYPES = {
    DataType.TRIGGER: [DataType.TRIGGER],
    DataType.IMAGE: [DataType.IMAGE, DataType.ATLAS],
    DataType.IMAGE_LIST: [DataType.IMAGE_LIST, DataType.IMAGE],  # Tek resim de liste olarak kabul edilebilir
    DataType.VIDEO: [DataType.VIDEO],
    DataType.FRAME_LIST: [DataType.FRAME_LIST, DataType.IMAGE_LIST],  # Frame listesi image listesi olarak da kabul edilebilir
    DataType.ATLAS: [DataType.ATLAS, DataType.IMAGE],
    DataType.STRING: [DataType.STRING],
    DataType.INT: [DataType.INT, DataType.FLOAT],
    DataType.FLOAT: [DataType.FLOAT, DataType.INT],
}

DEBUG = False


def print_error(*args):
    """Helper method which prints to console if `DEBUG` is set to `True`"""
    if DEBUG: print("Edge Validation Error:", *args)

def edge_validator_debug(input: 'Socket', output: 'Socket') -> bool:
    """This will consider edge always valid, however writes bunch of debug stuff into console"""
    for s in input.node.inputs+input.node.outputs: print("\t", s, "input" if s.is_input else "output")
    for s in output.node.inputs+output.node.outputs: print("\t", s, "input" if s.is_input else "output")

    return True

def edge_cannot_connect_two_outputs_or_two_inputs(input: 'Socket', output: 'Socket') -> bool:
    """Edge is invalid if it connects 2 output sockets or 2 input sockets"""
    if input.is_output and output.is_output:
        print_error("Connecting 2 outputs")
        return False

    if input.is_input and output.is_input:
        print_error("Connecting 2 inputs")
        return False

    return True

def edge_cannot_connect_input_and_output_of_same_node(input: 'Socket', output:'Socket') -> bool:
    """Edge is invalid if it connects the same node"""
    if input.node == output.node:
        print_error("Connecting the same node")
        return False

    return True

def edge_cannot_connect_input_and_output_of_different_type(input: 'Socket', output: 'Socket') -> bool:
    """Edge is invalid if it connects sockets with different colors"""
    # Socket'lerin veri türlerini al
    input_type = getattr(input, 'socket_type', None)
    output_type = getattr(output, 'socket_type', None)

    print_error(f"Type check: input={input_type}, output={output_type}")

    # END node özel durumu - herhangi bir veri türünü kabul edebilir
    if hasattr(input, 'node') and hasattr(input.node, 'node_type') and input.node.node_type == 'EndNode':
        # END node'a bağlanırken sadece temel tip kontrolü yap
        valid_end_types = [DataType.IMAGE, DataType.IMAGE_LIST, DataType.ATLAS, DataType.VIDEO]
        if isinstance(output_type, str):
            try:
                output_type = DataType(output_type)
            except Exception:
                pass
        result = output_type in valid_end_types
        print_error(f"END node check: {result}")
        return result

    # Enum'a dönüştür (string gelirse)
    if isinstance(input_type, str):
        try:
            input_type = DataType(input_type)
        except Exception:
            pass
    if isinstance(output_type, str):
        try:
            output_type = DataType(output_type)
        except Exception:
            pass

    print_error(f"After conversion: input={input_type}, output={output_type}")

    # Uyumlu tip kontrolü
    if input_type not in COMPATIBLE_TYPES:
        print_error(f"Input type {input_type} not in COMPATIBLE_TYPES")
        return False
    if output_type not in COMPATIBLE_TYPES[input_type]:
        print_error(f"Output type {output_type} not compatible with input type {input_type}")
        return False

    print_error("Type check passed")
    return True


def edge_node_connection_rules(input: 'Socket', output: 'Socket') -> bool:
    """Node bağlantı kurallarını kontrol eder (.md dosyasındaki kurallara göre)"""

    # Socket'lerin node'larını al
    input_node = getattr(input, 'node', None)
    output_node = getattr(output, 'node', None)

    print_error(f"Connection rule check: input_node={input_node}, output_node={output_node}")

    if not input_node or not output_node:
        print_error("Missing nodes")
        return False

    # Node türlerini al
    input_node_type = getattr(input_node, 'node_type', None)
    output_node_type = getattr(output_node, 'node_type', None)

    print_error(f"Node types: output={output_node_type} -> input={input_node_type}")

    if not input_node_type or not output_node_type:
        print_error("Missing node types")
        return False

    # Bağlantı kuralları (.md dosyasından)
    connection_rules = {
        'StartNode': ['image_source', 'multi_image_source', 'video_source'],
        'image_source': ['RemoveBackground', 'ImageResizer', 'EndNode'],
        'multi_image_source': ['RemoveBackground', 'ImageResizer', 'ImagePlacer', 'EndNode'],
        'video_source': ['ExtractFrames', 'EndNode'],
        'RemoveBackground': ['ImageResizer', 'ImagePlacer', 'EndNode'],
        'ImageResizer': ['ImagePlacer', 'RemoveBackground', 'EndNode'],
        'ImagePlacer': ['EndNode'],
        'ExtractFrames': ['RemoveBackground', 'ImageResizer', 'ImagePlacer', 'EndNode'],
        'ProcessFrames': ['RemoveBackground', 'ImageResizer', 'ImagePlacer', 'EndNode'],
        'FrameSampler': ['RemoveBackground', 'ImageResizer', 'ImagePlacer', 'EndNode'],
    }

    # Çıkış node'unun bağlanabileceği node'ları kontrol et
    allowed_connections = connection_rules.get(output_node_type, [])

    print_error(f"Allowed connections for {output_node_type}: {allowed_connections}")

    if input_node_type not in allowed_connections:
        print_error(f"Connection not allowed: {output_node_type} -> {input_node_type}")
        return False

    print_error("Connection rule passed")
    return True
