# -*- coding: utf-8 -*-
"""
A module containing ``NodeEditorWidget`` class
"""
import os
from qtpy.QtCore import Qt
from qtpy.QtGui import QBrush, QPen, QFont, QColor, QPixmap, QPainter
from qtpy.QtCore import QDataStream, Q<PERSON>Device
from qtpy.QtWidgets import QWidget, QVBoxLayout, QApplication, QMessageBox, QLabel, QGraphicsItem, QTextEdit, QPushButton, QHBoxLayout, QSizePolicy
import traceback

from nodeeditor.node_scene import Scene, InvalidFile
from nodeeditor.node_node import Node
from nodeeditor.node_edge import Edge, EDGE_TYPE_BEZIER
from nodeeditor.node_graphics_view import QDMGraphicsView
from nodeeditor.utils import dumpException
from apps.execution_node_editor.localization import i18n
from nodeeditor.node_add_menu import init_nodes_context_menu
from apps.execution_node_editor.conf import create_node, LISTBOX_MIMETYPE


class NodeEditorWidget(QWidget):
    Scene_class = Scene
    GraphicsView_class = QDMGraphicsView
    context_menu_open_count = 0  # Sağ tık context menu sayaç

    """The ``NodeEditorWidget`` class"""

    def __init__(self, parent: QWidget = None):
        """
        :param parent: parent widget
        :type parent: ``QWidget``

        :Instance Attributes:

        - **filename** - currently graph's filename or ``None``
        """
        super().__init__(parent)

        self.filename = None

        self.initUI()

        i18n.languageChanged.connect(self.updateDebugTexts)
        i18n.languageChanged.connect(self.setTitleBarText)

    def initUI(self):
        self.setStyleSheet('background-color: #232323; border: none;')
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.setLayout(self.layout)

        # --- Kısa highlight bar (ikon + başlık) ---
        header_widget = QWidget()
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(6, 4, 6, 4)
        header_layout.setSpacing(2)

        icon_path = 'apps/execution_node_editor/assets/icons/Editor/NodeEditor.png'
        icon_pixmap = QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        if icon_pixmap.isNull():
            pass
        white_icon = QPixmap(icon_pixmap.size())
        white_icon.fill(Qt.transparent)
        painter = QPainter(white_icon)
        painter.setCompositionMode(QPainter.CompositionMode_Source)
        painter.drawPixmap(0, 0, icon_pixmap)
        painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
        painter.fillRect(white_icon.rect(), Qt.white)
        painter.end()

        icon_label = QLabel()
        icon_label.setPixmap(white_icon)
        icon_label.setContentsMargins(0, 0, 2, 0)

        self.title_label = QLabel(i18n.get("new_graph_title"))
        self.title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.title_label.setStyleSheet("background: #313131; color: #cccccc; padding: 2px 16px; border-radius: 6px; font-size: 13px; font-weight: normal;")
        self.title_label.setContentsMargins(0, 0, 0, 0)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.setAlignment(Qt.AlignLeft)
        header_widget.setLayout(header_layout)
        header_widget.setStyleSheet("background: #232323; border-radius: 8px;")
        header_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.layout.addWidget(header_widget)
        # --- Sonu ---

        # crate graphics scene
        self.scene = self.__class__.Scene_class()
        self.setTitleBarText()
        # create graphics view
        self.view = self.__class__.GraphicsView_class(self.scene.grScene, self)
        self.view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.view.setMinimumSize(0, 0)
        self.view.setStyleSheet('background-color: #232323; border: none;')
        self.view.viewport().setStyleSheet('background-color: #232323;')
        self.view.setContentsMargins(0, 0, 0, 0)
        self.view.viewport().setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.view, stretch=1)
        self.layout.setStretch(0, 0)  # header
        self.layout.setStretch(1, 1)  # view

    def isModified(self) -> bool:
        """Has the `Scene` been modified?

        :return: ``True`` if the `Scene` has been modified
        :rtype: ``bool``
        """
        return self.scene.isModified()

    def isFilenameSet(self) -> bool:
        """Do we have a graph loaded from file or are we creating a new one?

        :return: ``True`` if filename is set. ``False`` if it is a new graph not yet saved to a file
        :rtype: ''bool''
        """
        return self.filename is not None

    def getSelectedItems(self) -> list:
        """Shortcut returning `Scene`'s currently selected items

        :return: list of ``QGraphicsItems``
        :rtype: list[QGraphicsItem]
        """
        return self.scene.getSelectedItems()

    def hasSelectedItems(self) -> bool:
        """Is there something selected in the :class:`nodeeditor.node_scene.Scene`?

        :return: ``True`` if there is something selected in the `Scene`
        :rtype: ``bool``
        """
        return self.getSelectedItems() != []

    def canUndo(self) -> bool:
        """Can Undo be performed right now?

        :return: ``True`` if we can undo
        :rtype: ``bool``
        """
        return self.scene.history.canUndo()

    def canRedo(self) -> bool:
        """Can Redo be performed right now?

        :return: ``True`` if we can redo
        :rtype: ``bool``
        """
        return self.scene.history.canRedo()

    def getUserFriendlyFilename(self) -> str:
        """Get user friendly filename. Used in the window title

        :return: just a base name of the file or `'New Graph'`
        :rtype: ``str``
        """
        name = os.path.basename(self.filename) if self.isFilenameSet() else i18n.get("new_graph_title")
        return name + ("*" if self.isModified() else "")

    def fileNew(self):
        """Empty the scene (create new file)"""
        self.scene.clear()
        self.filename = None
        self.scene.history.clear()
        self.scene.history.storeInitialHistoryStamp()



    def fileLoad(self, filename: str):
        """Load serialized graph from JSON file

        :param filename: file to load
        :type filename: ``str``
        """
        QApplication.setOverrideCursor(Qt.WaitCursor)
        try:
            self.scene.loadFromFile(filename)
            self.filename = filename
            self.scene.history.clear()
            self.scene.history.storeInitialHistoryStamp()
            return True
        except FileNotFoundError as e:
            dumpException(e)
            QMessageBox.warning(self, i18n.get("msgbox_title_load_error").format(os.path.basename(filename)), str(e).replace('[Errno 2]', ''))
            return False
        except InvalidFile as e:
            dumpException(e)
            QMessageBox.warning(self, i18n.get("msgbox_title_load_error").format(os.path.basename(filename)), str(e))
            return False
        finally:
            QApplication.restoreOverrideCursor()

    def fileSave(self, filename: str = None):
        """Save serialized graph to JSON file. When called with an empty parameter, we won't store/remember the filename.

        :param filename: file to store the graph
        :type filename: ``str``
        """
        if filename is not None:
            self.filename = filename

        if self.filename.endswith('.nes'):
            self.graphFilename = '.graph.json'.join(self.filename.rsplit('.nes', 1))
            self.sceneFilename = '.nes'.join(self.filename.rsplit('.nes', 1))
        else:
            self.graphFilename = self.filename + '.graph.json'
            self.sceneFilename = self.filename + '.nes'

        QApplication.setOverrideCursor(Qt.WaitCursor)
        self.scene.saveGraphToFile(self.graphFilename)
        self.scene.saveSceneToFile(self.sceneFilename)
        QApplication.restoreOverrideCursor()
        return True

    def addNodes(self):
        """Testing method to create 3 `Nodes` with 3 `Edges` connecting them"""
        node1 = Node(self.scene, "My Awesome Node 1",
                     inputs=[0, 0, 0], outputs=[1, 5])
        node2 = Node(self.scene, "My Awesome Node 2",
                     inputs=[3, 3, 3], outputs=[1])
        node3 = Node(self.scene, "My Awesome Node 3",
                     inputs=[2, 2, 2], outputs=[1])
        node1.setPos(-350, -250)
        node2.setPos(-75, 0)
        node3.setPos(200, -200)

        edge1 = Edge(
            self.scene, node1.outputs[0], node2.inputs[0], edge_type=EDGE_TYPE_BEZIER)
        edge2 = Edge(
            self.scene, node2.outputs[0], node3.inputs[0], edge_type=EDGE_TYPE_BEZIER)
        edge3 = Edge(
            self.scene, node1.outputs[0], node3.inputs[2], edge_type=EDGE_TYPE_BEZIER)

        self.scene.history.storeInitialHistoryStamp()

    def addCustomNode(self):
        """Testing method to create a custom Node with custom content"""
        from nodeeditor.node_content_widget import QDMNodeContentWidget
        from nodeeditor.node_serializable import Serializable

        class NNodeContent(QLabel):  # , Serializable):
            def __init__(self, node, parent=None):
                super().__init__(i18n.get("custom_node_content"))
                self.node = node
                self.setParent(parent)

        class NNode(Node):
            NodeContent_class = NNodeContent

        self.scene.setNodeClassSelector(lambda data: NNode)
        node = NNode(self.scene, "custom_node_title", inputs=[0, 1, 2])

    def addDebugContent(self):
        """Testing method to put random QGraphicsItems and elements into QGraphicsScene"""
        greenBrush = QBrush(Qt.green)
        outlinePen = QPen(Qt.black)
        outlinePen.setWidth(2)

        rect = self.grScene.addRect(-100, -100, 80,
                                    100, outlinePen, greenBrush)
        rect.setFlag(QGraphicsItem.ItemIsMovable)

        text = self.grScene.addText(
            i18n.get("debug_awesome_text"), QFont("Roboto"))
        text.setFlag(QGraphicsItem.ItemIsSelectable)
        text.setFlag(QGraphicsItem.ItemIsMovable)
        text.setDefaultTextColor(QColor.fromRgbF(1.0, 1.0, 1.0))
        text.setObjectName("debug_awesome_text_objname")

        widget1 = QPushButton(i18n.get("hello_world_btn"))
        widget1.setObjectName("debug_hello_world_btn_objname")
        proxy1 = self.grScene.addWidget(widget1)
        proxy1.setFlag(QGraphicsItem.ItemIsMovable)
        proxy1.setPos(0, 30)

        widget2 = QTextEdit()
        widget2.setObjectName("debug_textedit_objname")
        widget2.setPlaceholderText(i18n.get("debug_textedit_placeholder"))
        proxy2 = self.grScene.addWidget(widget2)
        proxy2.setFlag(QGraphicsItem.ItemIsSelectable)
        proxy2.setPos(0, 60)

        line = self.grScene.addLine(-200, -200, 400, -100, outlinePen)
        line.setFlag(QGraphicsItem.ItemIsMovable)
        line.setFlag(QGraphicsItem.ItemIsSelectable)

        self._debug_text = text
        self._debug_btn = widget1
        self._debug_textedit = widget2

    def updateDebugTexts(self):
        if hasattr(self, '_debug_text'):
            self._debug_text.setPlainText(i18n.get("debug_awesome_text"))
        if hasattr(self, '_debug_btn'):
            self._debug_btn.setText(i18n.get("hello_world_btn"))
        if hasattr(self, '_debug_textedit'):
            self._debug_textedit.setPlaceholderText(i18n.get("debug_textedit_placeholder"))

    def setTitleBarText(self):
        if hasattr(self, 'title_label'):
            self.title_label.setText(self.getUserFriendlyFilename())

    def resizeEvent(self, event):
        super().resizeEvent(event)

    def contextMenuEvent(self, event):
        # Başlangıçta olayı kabul edilmemiş olarak işaretle
        # Bu, sağ tıklama menüsünün her zaman açılmasını sağlar
        event.ignore()
        
        # Düğüm silme işleminden sonra bir süre sağ tıklama olaylarını engelleme mekanizmasını kontrol et
        from nodeeditor.node_graphics_node import QDMGraphicsNode
        import time
        current_time = time.time()
        time_since_last_delete = current_time - QDMGraphicsNode.last_node_deleted_time
        
        # Son düğüm silme işleminden bu yana 0.5 saniyeden az zaman geçmişse, olayı kabul et ve sonlandır
        if time_since_last_delete < 0.5:
            event.accept()
            return
        
        # Context menü sayısını artır
        NodeEditorWidget.context_menu_open_count += 1
        
        # Görünüm modunu ayarla ve seçimi temizle
        self.view.setDragMode(self.view.NoDrag)
        self.scene.grScene.clearSelection()
        self.view.viewport().update()
        
        # Düğüm ekleme menüsünü oluştur ve göster
        menu = init_nodes_context_menu(self)
        action = menu.exec_(event.globalPos())
        
        # Seçilen eyleme göre düğüm oluştur
        if action is not None:
            node_type = action.data()
            scene_pos = self.view.mapToScene(event.pos())
            node = create_node(self.scene, node_type)
            if node:
                node.setPos(scene_pos.x(), scene_pos.y())
                self.scene.history.storeHistory("Created node %s" % node.__class__.__name__)
        
        # Menü gösterildikten sonra, görünüm modunu tekrar RubberBandDrag olarak ayarla
        self.view.setDragMode(self.view.RubberBandDrag)
        
        # Olayı kabul et
        event.accept()

    def onDrop(self, event):
        if event.mimeData().hasFormat(LISTBOX_MIMETYPE):
            eventData = event.mimeData().data(LISTBOX_MIMETYPE)
            dataStream = QDataStream(eventData, QIODevice.ReadOnly)
            pixmap = QPixmap()
            dataStream >> pixmap
            node_type = dataStream.readQString()  # Gerçek node_type'ı oku
            display_name = dataStream.readQString()  # Display name'i oku (kullanmayabiliriz)
            mouse_position = event.pos()
            scene_position = self.scene.grScene.views()[0].mapToScene(mouse_position)
            try:
                node = create_node(self.scene, node_type)
                if node:
                    node.setPos(scene_position.x(), scene_position.y())
                    self.scene.history.storeHistory("Created node %s" % node.__class__.__name__)
            except Exception as e:
                pass
            event.setDropAction(Qt.MoveAction)
            event.accept()
        else:
            event.ignore()
