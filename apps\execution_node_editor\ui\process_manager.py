"""
İşlem yönetimi için ayrı modül
Mevcut window.py'deki process execution kodlarını organize eder
"""
import os
import sys
import subprocess
from sys import platform


class ProcessManager:
    """Grafik çalıştırma işlemlerini yöneten sınıf"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.process = None
        self.execution_subsystem_path = self._get_execution_path()
    
    def _get_execution_path(self):
        """Platform'a göre execution subsystem path'ini belirler"""
        exe_path = os.path.dirname(os.path.realpath(sys.argv[0]))
        
        if platform == "linux" or platform == "linux2":
            # Linux
            return os.path.join(exe_path, 'execution_subsystem', 'run_graph')
        elif platform == "darwin":
            # macOS
            return os.path.join(exe_path, 'execution_subsystem', 'run_graph')
        elif platform == "win32":
            # Windows
            return os.path.join(exe_path, 'execution_subsystem', 'run_graph.exe')
        else:
            # Varsayılan
            return os.path.join(exe_path, 'execution_subsystem', 'run_graph')
    
    def execute_process(self, graph_file):
        """Grafik dosyasını çalıştırır"""
        # Mevcut işlemi durdur
        self._terminate_current_process()
        
        # Yeni işlemi başlat
        args = [self.execution_subsystem_path, graph_file]
        
        try:
            if platform == "win32":
                flags = subprocess.CREATE_NO_WINDOW
                self.process = subprocess.Popen(args, creationflags=flags)
            else:
                self.process = subprocess.Popen(args)
            
            return True
        except Exception as e:
            return False
    
    def _terminate_current_process(self):
        """Mevcut işlemi sonlandırır"""
        if self.process is not None:
            if self.process.poll() is None:
                # İşlem hala çalışıyor, sonlandır
                self.process.terminate()
                try:
                    self.process.wait(1)  # 1 saniye bekle
                except:
                    # Zorla sonlandır
                    self.process.kill()
    
    def run_graph(self):
        """Ana grafik çalıştırma metodu"""
        # Önce grafiği kaydet
        ok, graph_file = self.main_window.onFileAutosave()
        
        if ok:
            success = self.execute_process(graph_file)
            if success:
                self.main_window.statusBar().showMessage(f"Graph execution started: {graph_file}", 3000)
            else:
                self.main_window.statusBar().showMessage("Failed to start graph execution", 3000)
        else:
            self.main_window.statusBar().showMessage("Failed to save graph before execution", 3000)
    
    def is_process_running(self):
        """İşlemin çalışıp çalışmadığını kontrol eder"""
        if self.process is None:
            return False
        return self.process.poll() is None
    
    def get_process_status(self):
        """İşlem durumunu döndürür"""
        if self.process is None:
            return "No process"
        elif self.process.poll() is None:
            return "Running"
        else:
            return f"Finished (exit code: {self.process.returncode})"
    
    def stop_process(self):
        """İşlemi durdurur"""
        self._terminate_current_process()
        if self.main_window:
            self.main_window.statusBar().showMessage("Process stopped", 2000)
    
    def cleanup(self):
        """Kaynakları temizler"""
        self._terminate_current_process()
        self.process = None
