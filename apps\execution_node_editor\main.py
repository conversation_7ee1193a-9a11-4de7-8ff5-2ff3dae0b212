import os, sys
from PyQt5 import Qt<PERSON>ore, QtGui
from qtpy.QtWidgets import QApplication
import ctypes
from sys import platform


sys.path.insert(0, os.path.join( os.path.dirname(__file__), "..", ".." ))

from apps.execution_node_editor.ui.window import ExecutionNodeEditorWindow
from apps.execution_node_editor.core.node_type_definition import read_node_type_definitions_from_dirs
from apps.execution_node_editor.conf import register_node_types


if __name__ == '__main__':
    app = QApplication(sys.argv)
    # from PyQt5.QtWidgets import QToolTip
    # QToolTip.setStyleSheet("QToolTip { background-color: #222; color: #fff; border: 1px solid #555; font-size: 13px; }")

    exe_path = os.path.dirname(os.path.realpath(sys.argv[0]))
    assets_dir = os.path.join(exe_path, 'assets') 

    for (dirpath, dirnames, filenames) in os.walk(os.path.join(assets_dir, 'fonts')):
        for f in filenames:
            font_id = QtGui.QFontDatabase.addApplicationFont(os.path.join(dirpath, f))
            if QtGui.QFontDatabase.applicationFontFamilies(font_id) == -1:
                sys.exit(-1)

    # print(QStyleFactory.keys())
    app.setStyle('Fusion')

    # QPalette ile ilgili kodları yoruma alıyorum
    # from PyQt5.QtGui import QPalette, QColor
    # palette = QPalette()
    # palette.setColor(QPalette.Window, QColor("#2d2d2d"))
    # palette.setColor(QPalette.WindowText, QColor("#e0e0e0"))
    # palette.setColor(QPalette.Base, QColor("#2d2d2d"))
    # palette.setColor(QPalette.AlternateBase, QColor("#383838"))
    # palette.setColor(QPalette.ToolTipBase, QColor("#e0e0e0"))
    # palette.setColor(QPalette.ToolTipText, QColor("#e0e0e0"))
    # palette.setColor(QPalette.Text, QColor("#e0e0e0"))
    # palette.setColor(QPalette.Button, QColor("#3c3c3c"))
    # palette.setColor(QPalette.ButtonText, QColor("#e0e0e0"))
    # palette.setColor(QPalette.BrightText, QColor("#ff0000"))
    # palette.setColor(QPalette.Highlight, QColor("#4a90e2"))
    # palette.setColor(QPalette.HighlightedText, QColor("#ffffff"))
    # app.setPalette(palette)

    # QSS dosyasını dinamik olarak yükle
    qss_path = os.path.join(os.path.dirname(__file__), "assets", "qss", "nodeeditor-light.qss")
    with open(qss_path, "r", encoding="utf-8") as f:
        app.setStyleSheet(f.read())

    app_icon = QtGui.QIcon()
    app_icon.addFile(os.path.join(assets_dir, 'icons/16x16.png'), QtCore.QSize(16,16))
    app_icon.addFile(os.path.join(assets_dir, 'icons/24x24.png'), QtCore.QSize(24,24))
    app_icon.addFile(os.path.join(assets_dir, 'icons/32x32.png'), QtCore.QSize(32,32))
    app_icon.addFile(os.path.join(assets_dir, 'icons/48x48.png'), QtCore.QSize(48,48))
    app_icon.addFile(os.path.join(assets_dir, 'icons/64x64.png'), QtCore.QSize(64,64))
    app_icon.addFile(os.path.join(assets_dir, 'icons/128x128.png'), QtCore.QSize(128,128))
    app_icon.addFile(os.path.join(assets_dir, 'icons/256x256.png'), QtCore.QSize(256,256))
    app.setWindowIcon(app_icon)


    if platform == "win32":
        # Windows...
        #This will make sure that the app icon is set in the taskbar on windows
        # See https://stackoverflow.com/questions/1551605/how-to-set-applications-taskbar-icon-in-windows-7/1552105#1552105 
        myappid = u'no-company.node-editor.execution-graph-editor.1.0' # arbitrary string
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)

    wnd = ExecutionNodeEditorWindow()
    wnd.setWindowIcon(app_icon)
    wnd.show()

    # Node tiplerini mantıksal klasörlerden yükle ve kaydet
    node_dir = os.path.join(os.path.dirname(__file__), 'node')
    categorized_node_type_definitions = read_node_type_definitions_from_dirs(node_dir)
    for category, node_type_definitions in categorized_node_type_definitions.items():
        register_node_types(node_type_definitions, category=category)
    # Node tipleri yüklendikten sonra paneli güncelle
    wnd.nodesDockManager.window.nodesListWidget.updateTexts()

    wnd.actNew.trigger()
    if len(sys.argv) == 2:
        wnd.openFile(sys.argv[1])

    # Yaygın ikonları önceden yükle (geçici olarak devre dışı)
    # from apps.execution_node_editor.utils.icon_manager import preload_common_icons
    # preload_common_icons()



    sys.exit(app.exec_())
