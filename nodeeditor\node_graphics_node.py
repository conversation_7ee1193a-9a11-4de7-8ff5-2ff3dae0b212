# -*- coding: utf-8 -*-
"""
A module containing Graphics representation of :class:`~nodeeditor.node_node.Node`
"""
from PyQt5.QtCore import QPoint, QRectF, Qt, QTimer
from PyQt5.QtGui import QFontMetrics, QTextBlockFormat, QTextCursor, QPixmap, QPainter, QPen, QFont, QColor
from PyQt5.QtWidgets import (QGraphicsDropShadowEffect, QGraphicsSceneHoverEvent, QGraphicsPixmapItem, 
                             QToolTip, QLabel, QMenu, QAction)
from qtpy.QtWidgets import QGraphicsItem, QWidget, QGraphicsTextItem
from qtpy.QtGui import QFont, QColor, QPen, QBrush, QPainterPath, QPainter
from qtpy.QtCore import Qt
from apps.execution_node_editor.localization import i18n
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from nodeeditor.node_node import Node

import math
import time

# Özel tooltip penceresi
class CustomTooltip(QLabel):
    _instance = None
    
    @classmethod
    def getInstance(cls):
        if cls._instance is None:
            cls._instance = CustomTooltip()
        return cls._instance
    
    def __init__(self):
        super().__init__(None, Qt.ToolTip | Qt.FramelessWindowHint)
        self.setStyleSheet("""
            background-color: #2a2a2a;
            color: white;
            border: 1px solid #3498db;
            border-radius: 4px;
            padding: 6px;
            font-family: 'Roboto';
            font-size: 10pt;
        """)
        self.setWordWrap(True)
        self.setMaximumWidth(300)  # Maksimum genişlik
        self.hide()
    
    def showTooltip(self, text, pos):
        if not text:
            self.hide()
            return
        
        # Metni yan yana sıralamak için HTML formatını kullan
        # Alt alta olan satırları yan yana göstermek için boşlukları boşluk karakteri ile değiştir
        formatted_text = text.replace("\n", " ")
        self.setText(formatted_text)
        self.adjustSize()
        
        # Fare imlecinin sağında ve altında göster
        pos = QPoint(pos.x() + 25, pos.y() + 10)
        self.move(pos)
        self.show()

class HelpIconItem(QGraphicsPixmapItem):
    def __init__(self, pixmap, tooltip_text, parent=None):
        super().__init__(pixmap, parent)
        self.setAcceptHoverEvents(True)
        self.tooltip_text = tooltip_text
        self.custom_tooltip = CustomTooltip.getInstance()

    def hoverEnterEvent(self, event):
        # Özel tooltip'i göster
        if self.tooltip_text:
            self.custom_tooltip.showTooltip(self.tooltip_text, event.screenPos())
        super().hoverEnterEvent(event)
        
    def hoverLeaveEvent(self, event):
        # Özel tooltip'i gizle
        self.custom_tooltip.hide()
        super().hoverLeaveEvent(event)

class QDMGraphicsNode(QGraphicsItem):
    """Class describing Graphics representation of :class:`~nodeeditor.node_node.Node`"""
    
    # Düğüm silme işleminden sonra sağ tıklama olaylarını engelleme mekanizması için sınıf değişkeni
    # Başlangıç değerini şu anki zamanla ayarla
    last_node_deleted_time = time.time() - 10  # 10 saniye öncesine ayarla, böylece başlangıçta engelleme olmaz

    def __init__(self, node: 'Node', parent: QWidget = None):
        """
        :param node: reference to :class:`~nodeeditor.node_node.Node`
        :type node: :class:`~nodeeditor.node_node.Node`
        :param parent: parent widget
        :type parent: QWidget

        :Instance Attributes:

            - **node** - reference to :class:`~nodeeditor.node_node.Node`
        """
        super().__init__(parent)
        self.node = node
        from apps.execution_node_editor.localization import i18n
        i18n.languageChanged.connect(self.updateTitleLocalization)

        # init our flags
        self.hovered = False
        self._was_moved = False
        self._last_selected_state = False
        self.thumb_layer = None  # ThumbLayer referansı
        self.initSizes(0)
        self.initAssets()
        self.initUI()

    @property
    def content(self):
        """Reference to `Node Content`"""
        return self.node.content if self.node else None

    @property
    def title(self):
        """title of this `Node`

        :getter: current Graphics Node title
        :setter: stores and make visible the new title
        :type: str
        """
        return self._title

    @title.setter
    def title(self, value):
        from apps.execution_node_editor.localization import i18n
        translated = i18n.get(value)
        self._title = translated if translated else value
        
        # Uzun başlıkları kısaltma işlemi
        display_text = self._title
        
        # Başlığın uzunluğunu kontrol et
        if hasattr(self, 'title_item') and self.title_item:
            # Sağda ikon için yer bırak
            max_width = self.width - 40 if hasattr(self, 'width') else 140
            
            # Mevcut fontu al veya varsayılan font oluştur
            font = self.title_item.font() if hasattr(self.title_item, 'font') else QFont("Roboto", 12, QFont.Bold)
            metrics = QFontMetrics(font)
            
            # Başlık çok uzunsa kısalt
            if metrics.width(display_text) > max_width:
                ellipsis = "..."
                # Başlığı kısaltıp sonuna "..." ekle
                for i in range(len(self._title) - 1, 0, -1):
                    shortened = self._title[:i] + ellipsis
                    if metrics.width(shortened) <= max_width:
                        display_text = shortened
                        break
            
            # Orijinal başlığı tooltip olarak ayarla
            self.title_item.setToolTip(self._title)
        
        # Kısaltılmış başlığı görüntüle
        if hasattr(self, 'title_item') and self.title_item:
            self.title_item.setPlainText(display_text)

    def initUI(self):
        """Set up this ``QGraphicsItem``"""
        self.setFlag(QGraphicsItem.ItemIsSelectable)
        self.setFlag(QGraphicsItem.ItemIsMovable)
        self.setAcceptHoverEvents(True)

        self.initTitle()
        self.initContent()

    def initSizes(self):
        """Set up internal attributes like `width`, `height`, etc."""
        self.width = 180
        self.height = 240
        self.edge_roundness = 1.0
        self.edge_padding = 10.0
        self.title_height = 24.0
        self.title_horizontal_padding = 4.0
        self.title_vertical_padding = 4.0

        # Dinamik boyutlandırma için
        self.min_width = 150
        self.min_height = 80
        self.max_width = 500
        self.max_height = 800
        self.auto_resize = True

    def initAssets(self):
        """Initialize ``QObjects`` like ``QColor``, ``QPen`` and ``QBrush``"""
        self._title_color = QColor("#f5f5f5")
        self._title_font = QFont("Roboto", 12, QFont.Bold)

        self._color_hovered = QColor("#52ffb9")
        self._color_selected = QColor("#28daed")
        self._color_dirty = QColor("#ed8836")
        self._color_dirty_selected = QColor("#ffc519")

        self._pen_default = QPen(QColor("#00000000"))
        self._pen_default.setWidthF(0)
        self._pen_selected = QPen(self._color_selected)
        self._pen_selected.setWidthF(1.5)
        self._pen_dirty = QPen(self._color_dirty)
        self._pen_dirty.setWidthF(1.5)
        self._pen_dirty_selected = QPen(self._color_dirty_selected)
        self._pen_dirty_selected.setWidthF(1.5)
        self._pen_hovered = QPen(self._color_hovered)
        self._pen_hovered.setWidthF(2)

        self._brush_title = QBrush(QColor("#1b202c"))
        self._brush_title_hover = QBrush(QColor("#222736"))
        self._brush_title_selected = QBrush(QColor("#282f40"))
        self._brush_title_dirty = QBrush(self._color_dirty)
        self._brush_title_dirty_selected = QBrush(self._color_dirty_selected)
        self._brush_title_dirty_hovered = QBrush(QColor("#f29d33"))
        self._brush_background = QBrush(QColor("#151a24"))
        self._brush_hover = QBrush(QColor("#151a24"))

        shadowX = 0
        shadowY = 0
        self._shadow = QGraphicsDropShadowEffect(blurRadius=0,
                                                  offset=QPoint(shadowX, shadowY))
        self._color_shadow = QColor('#000000')
        self._shadow.setColor(self._color_shadow)
        self._shadow.setEnabled(True)
        self.setGraphicsEffect(self._shadow)

        self.width += shadowX
        self.height += shadowY

    def onSelected(self):
        """Our event handling when the node was selected"""
        self.node.scene.grScene.itemSelected.emit()

    def doSelect(self, new_state=True):
        """Safe version of selecting the `Graphics Node`. Takes care about the selection state flag used internally

        :param new_state: ``True`` to select, ``False`` to deselect
        :type new_state: ``bool``
        """
        self.setSelected(new_state)
        self._last_selected_state = new_state
        if new_state:
            self.onSelected()

    def setThumbLayer(self, thumb_layer):
        """ThumbLayer referansını ayarla"""
        self.thumb_layer = thumb_layer
        
    def getHeight(self):
        """Düğümün yüksekliğini döndür"""
        return self.height
        
    def getWidth(self):
        """Düğümün genişliğini döndür"""
        return self.width
    
    def mouseMoveEvent(self, event):
        """Overridden event to detect that we moved with this `Node`"""
        super().mouseMoveEvent(event)

        # optimize me! just update the selected nodes
        for node in self.scene().scene.nodes:
            if node.grNode.isSelected():
                node.updateConnectedEdges()
        self._was_moved = True
        
        # ThumbLayer varsa konumunu güncelle
        if self.thumb_layer:
            # Düğüm hareket ettirildiğinde ThumbLayer'i de hareket ettir
            self.thumb_layer.update_position()

    def mouseReleaseEvent(self, event):
        """Overriden event to handle when we moved, selected or deselected this `Node`"""
        super().mouseReleaseEvent(event)

        # handle when grNode moved
        if self._was_moved:
            self._was_moved = False
            self.node.scene.history.storeHistory(
                "Node moved", setModified=True)

            self.node.scene.resetLastSelectedStates()
            self.doSelect()     # also trigger itemSelected when node was moved

            # we need to store the last selected state, because moving does also select the nodes
            self.node.scene._last_selected_items = self.node.scene.getSelectedItems()

            # now we want to skip storing selection
            return

        # handle when grNode was clicked on
        if self._last_selected_state != self.isSelected() or self.node.scene._last_selected_items != self.node.scene.getSelectedItems():
            self.node.scene.resetLastSelectedStates()
            self._last_selected_state = self.isSelected()
            self.onSelected()

    def mouseDoubleClickEvent(self, event):
        """Overriden event for doubleclick. Resend to `Node::onDoubleClicked`"""
        self.node.onDoubleClicked(event)

    def hoverEnterEvent(self, event: 'QGraphicsSceneHoverEvent') -> None:
        """Handle hover effect"""
        self.hovered = True
        self.update()

    def hoverLeaveEvent(self, event: 'QGraphicsSceneHoverEvent') -> None:
        """Handle hover effect"""
        self.hovered = False
        self.update()

    def boundingRect(self) -> QRectF:
        """Defining Qt' bounding rectangle"""
        return QRectF(
            0,
            0,
            self.width,
            self.height
        ).normalized()

    def initTitle(self):
        """Set up the title Graphics representation: font, color, position, etc."""
        self.title_item = QGraphicsTextItem(self)
        self.title_item.node = self.node
        self.title_item.setDefaultTextColor(self._title_color)
        
        # Orijinal başlık metnini al
        original_title_text = self.node.title
        # Başlangıçta kısaltılmış başlık orijinal başlık ile aynı
        title_text = original_title_text
        
        # Sağda ikon için yer bırak
        max_width = self.width - 40
        font_size = 16
        min_font_size = 12
        font = QFont("Roboto", font_size, QFont.Bold)
        metrics = QFontMetrics(font)
        
        # Önce font boyutunu küçültmeyi dene
        while font_size >= min_font_size:
            font = QFont("Roboto", font_size, QFont.Bold)
            metrics = QFontMetrics(font)
            text_width = metrics.width(title_text)
            if text_width <= max_width:
                break
            font_size -= 1
        
        # Eğer font boyutunu küçültmek yeterli değilse, metni kısalt
        if metrics.width(title_text) > max_width:
            # Metni kısaltma işlemi
            ellipsis = "..."
            # Başlığı kısaltıp sonuna "..." ekle
            for i in range(len(original_title_text), 0, -1):
                shortened_text = original_title_text[:i] + ellipsis
                if metrics.width(shortened_text) <= max_width:
                    title_text = shortened_text
                    break
        
        # Orijinal başlığı tooltip olarak ayarla (fare üzerinde geldiğinde görünsün)
        self.title_item.setToolTip(original_title_text)
        
        self.title_item.setFont(font)
        
        # Başlığı biraz daha yukarıda tut
        x = 12
        y = 4
        self.title_item.setX(x)
        self.title_item.setY(y)
        self.title_item.setTextWidth(self.width)
        self.title_item.setPlainText(title_text)

        # Kullanıcının kendi ikonunu yükle
        icon_path = "apps/execution_node_editor/assets/icons/Editor/question_mark.png"  # Kullanıcının belirttiği ikon yolu
        pixmap = QPixmap(icon_path)
        icon_size = 16
        if not pixmap.isNull():
            # PNG'yi beyaza boyama
            image = pixmap.toImage()
            for x in range(image.width()):
                for y in range(image.height()):
                    color = image.pixelColor(x, y)
                    alpha = color.alpha()
                    if alpha > 0:
                        image.setPixelColor(x, y, QColor(255, 255, 255, alpha))
            pixmap = QPixmap.fromImage(image)
            pixmap = pixmap.scaled(icon_size, icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            from apps.execution_node_editor.localization import i18n
            desc_key = self.node.getNodeDescription() if hasattr(self.node, 'getNodeDescription') else ""
            localized_desc = i18n.get(desc_key) if desc_key else ""
            self.question_icon = HelpIconItem(pixmap, localized_desc, self)
            self.question_icon.setPos(self.width - 24, 7)
        else:
            self.question_icon = None  # İkon yüklenemezse ekleme

    def initContent(self):
        """Initialize content widget with dynamic sizing support"""
        if self.content is not None:
            from PyQt5.QtWidgets import QGraphicsProxyWidget, QWidget

            # Content widget'a geometry change listener ekle
            if hasattr(self.content, 'contentSizeChanged'):
                self.content.contentSizeChanged.connect(self.onContentSizeChanged)

            self.updateContentGeometry()

    def updateContentGeometry(self):
        """Content widget'ın geometrisini güncelle"""
        if self.content is None:
            return

        from PyQt5.QtWidgets import QGraphicsProxyWidget, QWidget

        # Mevcut content area boyutunu hesapla
        content_width = self.width - 2 * self.edge_padding
        content_height = self.height - self.title_height - 2 * self.edge_padding

        if isinstance(self.content, QWidget):
            # QWidget ise proxy kullan
            if not hasattr(self, 'grContent') or self.grContent is None:
                self.grContent = QGraphicsProxyWidget(self)
                self.grContent.setWidget(self.content)

            self.grContent.setPos(self.edge_padding, self.title_height + self.edge_padding)
            self.grContent.setGeometry(QRectF(0, 0, content_width, content_height))

            # Content widget'ın boyutunu ayarla
            if hasattr(self.content, 'setFixedSize'):
                self.content.setFixedSize(content_width, content_height)
            elif hasattr(self.content, 'resize'):
                self.content.resize(content_width, content_height)
        else:
            # QGraphicsWidget ise direkt kullan
            self.content.setParentItem(self)
            self.content.setPos(self.edge_padding, self.title_height + self.edge_padding)
            if hasattr(self.content, 'resize'):
                self.content.resize(content_width, content_height)
            self.grContent = self.content

    def onContentSizeChanged(self):
        """Content boyutu değiştiğinde çağrılır"""
        if self.auto_resize and self.content:
            self.updateGeometry()

    def updateGeometry(self):
        """Node geometrisini content'e göre güncelle"""
        if not self.auto_resize or not self.content:
            return

        # Content'in istediği boyutu al
        if hasattr(self.content, 'sizeHint'):
            content_size = self.content.sizeHint()
        elif hasattr(self.content, 'size'):
            content_size = self.content.size()
        else:
            return

        # Yeni node boyutunu hesapla
        new_width = content_size.width() + 2 * self.edge_padding
        new_height = content_size.height() + self.title_height + 2 * self.edge_padding

        # Min/max sınırları uygula
        new_width = max(self.min_width, min(new_width, self.max_width))
        new_height = max(self.min_height, min(new_height, self.max_height))

        # Boyut değişti mi kontrol et
        if abs(new_width - self.width) > 1 or abs(new_height - self.height) > 1:
            old_width, old_height = self.width, self.height
            self.width = new_width
            self.height = new_height

            # Content geometrisini güncelle
            self.updateContentGeometry()

            # Help icon pozisyonunu güncelle
            if hasattr(self, 'question_icon') and self.question_icon:
                self.question_icon.setPos(self.width - 24, 7)

            # Socket pozisyonlarını güncelle
            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()

            # Scene'i güncelle
            self.update()

            # Bounding rect değişti, scene'e bildir
            self.prepareGeometryChange()

    def onPropertyChanged(self, property_name: str, old_value, new_value):
        """Property değiştiğinde çağrılır"""
        # Boyut ile ilgili property'ler değiştiyse geometry'yi güncelle
        if property_name in ['node_title'] or self.auto_resize:
            self.updateGeometry()

    def setAutoResize(self, enabled: bool):
        """Otomatik boyutlandırmayı aç/kapat"""
        self.auto_resize = enabled
        if enabled:
            self.updateGeometry()

    def setMinimumSize(self, width: int, height: int):
        """Minimum boyutu ayarla"""
        self.min_width = width
        self.min_height = height
        if self.auto_resize:
            self.updateGeometry()

    def setMaximumSize(self, width: int, height: int):
        """Maksimum boyutu ayarla"""
        self.max_width = width
        self.max_height = height
        if self.auto_resize:
            self.updateGeometry()

    def paint(self, painter, QStyleOptionGraphicsItem, widget=None):
        """Painting the rounded rectanglar `Node`"""
        # title
        path_title = QPainterPath()
        path_title.setFillRule(Qt.WindingFill)
        path_title.addRoundedRect(
            0, 0, self.width, self.title_height, self.edge_roundness, self.edge_roundness)
        path_title.addRect(0, self.title_height - self.edge_roundness,
                           self.edge_roundness, self.edge_roundness)
        path_title.addRect(self.width - self.edge_roundness, self.title_height -
                           self.edge_roundness, self.edge_roundness, self.edge_roundness)
        painter.setPen(Qt.NoPen)
        if self.hovered:
            if self.node.isDirty():
                painter.setBrush(self._brush_title_dirty_hovered)
            else:
                painter.setBrush(self._brush_title_hover)
        else:
            if self.node.isDirty():
                painter.setBrush(self._brush_title_dirty)
            else:
                painter.setBrush(self._brush_title)

        if self.isSelected():
            self._shadow.setBlurRadius(20)
            if self.node.isDirty():
                painter.setBrush(self._brush_title_dirty_selected)
                self._shadow.setColor(self._color_dirty)
            else:
                painter.setBrush(self._brush_title_selected)
                self._shadow.setColor(self._color_selected)

        else:
            self._shadow.setColor(QColor('#00000000'))
            self._shadow.setBlurRadius(0)


        painter.drawPath(path_title.simplified())

        # content
        path_content = QPainterPath()
        path_content.setFillRule(Qt.WindingFill)
        path_content.addRoundedRect(0, self.title_height, self.width, self.height -
                                    self.title_height, self.edge_roundness, self.edge_roundness)
        path_content.addRect(0, self.title_height,
                             self.edge_roundness, self.edge_roundness)
        path_content.addRect(self.width - self.edge_roundness,
                             self.title_height, self.edge_roundness, self.edge_roundness)
        painter.setPen(Qt.NoPen)

        if self.hovered:
            painter.setBrush(self._brush_hover)
            painter.drawPath(path_content.simplified())

        else:
            painter.setBrush(self._brush_background)
            painter.drawPath(path_content.simplified())

            # outline
        path_outline = QPainterPath()
        painter.setBrush(Qt.NoBrush)
        if self.isSelected():
            painter.setPen(self._pen_selected)
            path_outline.addRoundedRect(
                0, 0, self.width, self.height, self.edge_roundness, self.edge_roundness)
        else:
            painter.setPen(self._pen_default)
            path_outline.addRoundedRect(-0.5, -0.5, self.width+1,
                                        self.height+1, self.edge_roundness, self.edge_roundness)

        if self.node.isDirty():
            if self.isSelected():
                painter.setPen(self._pen_dirty_selected)
            else:
                painter.setPen(self._pen_dirty)

        painter.drawPath(path_outline.simplified())

    def updateTitleLocalization(self):
        # Dil değiştiğinde başlığı güncelle
        # Burada self.title setter'a çağrı yapılıyor, bu da title_item'a yeni değeri atar
        self.title = self.node.title
        
        # Başlığı yeniden biçimlendir (uzunsa kısalt)
        # Orijinal başlık metnini al
        original_title_text = self._title
        # Başlangıçta kısaltılmış başlık orijinal başlık ile aynı
        title_text = original_title_text
        
        # Sağda ikon için yer bırak
        max_width = self.width - 40
        
        # Mevcut fontu al
        font = self.title_item.font()
        metrics = QFontMetrics(font)
        
        # Metnin genişliğini kontrol et
        if metrics.width(title_text) > max_width:
            # Metni kısaltma işlemi
            ellipsis = "..."
            # Başlığı kısaltıp sonuna "..." ekle
            for i in range(len(original_title_text), 0, -1):
                shortened_text = original_title_text[:i] + ellipsis
                if metrics.width(shortened_text) <= max_width:
                    title_text = shortened_text
                    break
            
            # Kısaltılmış metni ayarla
            self.title_item.setPlainText(title_text)
        
        # Orijinal başlığı tooltip olarak ayarla (fare üzerinde geldiğinde görünsün)
        self.title_item.setToolTip(original_title_text)
        
        # Tooltip'i de güncelle
        if hasattr(self, "question_icon") and self.question_icon:
            desc_key = self.node.getNodeDescription() if hasattr(self.node, 'getNodeDescription') else ""
            localized_desc = i18n.get(desc_key) if desc_key else ""
            self.question_icon.tooltip_text = localized_desc
            from PyQt5.QtWidgets import QToolTip
            QToolTip.hideText()

    def contextMenuEvent(self, event):
        # İç içe sağ tıklama olaylarını önlemek için zaman kontrolü
        import time
        current_time = time.time()
        time_since_last_delete = current_time - QDMGraphicsNode.last_node_deleted_time

        # Son düğüm silme işleminden bu yana 0.5 saniyeden az zaman geçmişse, olayı kabul et ve sonlandır
        if time_since_last_delete < 0.5:
            event.accept()
            return

        # System node'ları için context menu gösterme
        if hasattr(self.node, 'settings') and self.node.settings.get('is_system_node', False):
            event.accept()
            return

        menu = QMenu()
        menu.setStyleSheet("QMenu { color: white; background-color: #222736; } QMenu::item:selected { background-color: #52ffb9; color: black; }")
        delete_action = QAction('Nodu Sil', menu)
        menu.addAction(delete_action)
        action = menu.exec_(event.screenPos())

        if action == delete_action:
            # Önce olayı kabul et ve işlemi sonlandır
            # Böylece düğüm silindikten sonra başka bir menü açılmaz
            event.accept()

            # Görünümü sıfırla
            if self.scene() and self.scene().views():
                view = self.scene().views()[0]
                if hasattr(view, 'setDragMode'):
                    view.setDragMode(view.RubberBandDrag)

            # Son düğüm silme zamanını güncelle
            QDMGraphicsNode.last_node_deleted_time = time.time()

            # Düğümü sil - bu işlem sonrasında bu nesne geçersiz olabilir
            self.node.remove()

            # Kesinlikle işlemi burada sonlandır
            return
        else:
            # Herhangi bir eylem seçilmediyse olayı yok say
            event.ignore()
