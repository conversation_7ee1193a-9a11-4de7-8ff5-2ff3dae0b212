"""
Scene dosya işlemleri için ayrı modül
"""
import os
import json
from pathlib import Path
from collections import OrderedDict
from nodeeditor.utils import dumpException


class InvalidFile(Exception):
    """Geçersiz dosya hatası"""
    pass


class SceneFileManager:
    """Scene dosya kaydetme/yükleme işlemlerini yöneten sınıf"""
    
    def __init__(self, scene):
        self.scene = scene
    
    def save_graph_to_file(self, filename: str):
        """Grafiği execution format'ında kaydeder"""
        try:
            graph = self._create_graph_data()
            json_graph = json.dumps(graph, indent=2)
            
            with open(filename, "w") as file:
                file.write(json_graph)
            
            self.scene.has_been_modified = False
            return True
            
        except Exception as e:
            dumpException(e)
            return False
    
    def save_scene_to_file(self, filename: str):
        """Scene'i tam format'ta kaydeder"""
        try:
            scene_data = self.scene.serialize()
            json_data = json.dumps(scene_data, indent=2)
            
            with open(filename, "w") as file:
                file.write(json_data)
            
            self.scene.has_been_modified = False
            self.scene.filename = filename
            return True
            
        except Exception as e:
            dumpException(e)
            return False
    
    def load_from_file(self, filename: str):
        """Dosyadan scene yükler"""
        try:
            with open(filename, "r") as file:
                raw_data = file.read()
            
            try:
                data = json.loads(raw_data, encoding='utf-8')
                self.scene.filename = filename
                success = self.scene.deserialize(data)
                
                if success:
                    self.scene.has_been_modified = False
                    return True
                else:
                    return False
                    
            except json.JSONDecodeError:
                raise InvalidFile(f"{os.path.basename(filename)} is not a valid JSON file")
                
        except Exception as e:
            dumpException(e)
            raise e
    
    def _create_graph_data(self):
        """Execution için graph data oluşturur"""
        graph = {}
        nodes_dict = {}
        
        # Node'ları serialize et
        for node in self.scene.nodes:
            node_dict = {
                "type": node.node_type,
                "settings": node.settings
            }
            nodes_dict[node.title] = node_dict
        
        # Edge'leri serialize et
        edges_list = []
        for edge in self.scene.edges:
            if edge.start_socket and edge.end_socket:
                start = f"{edge.start_socket.node.title}:{edge.start_socket.socket_name}"
                end = f"{edge.end_socket.node.title}:{edge.end_socket.socket_name}"
                edges_list.append([start, end])
        
        # Graph data'yı oluştur
        graph["name"] = Path(self.scene.filename).stem if self.scene.filename else "untitled"
        graph["nodes"] = nodes_dict
        graph["connections"] = edges_list
        
        return graph
    
    def get_file_info(self, filename: str = None):
        """Dosya bilgilerini döndürür"""
        target_file = filename or self.scene.filename
        
        if not target_file or not os.path.exists(target_file):
            return None
        
        try:
            stat = os.stat(target_file)
            return {
                'filename': os.path.basename(target_file),
                'full_path': target_file,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'exists': True
            }
        except Exception:
            return None
    
    def validate_file(self, filename: str):
        """Dosyanın geçerli olup olmadığını kontrol eder"""
        try:
            if not os.path.exists(filename):
                return False, "File does not exist"
            
            with open(filename, "r") as file:
                raw_data = file.read()
            
            try:
                data = json.loads(raw_data)
                
                # Temel yapıyı kontrol et
                required_keys = ['id', 'nodes', 'edges']
                for key in required_keys:
                    if key not in data:
                        return False, f"Missing required key: {key}"
                
                return True, "Valid scene file"
                
            except json.JSONDecodeError as e:
                return False, f"Invalid JSON: {str(e)}"
                
        except Exception as e:
            return False, f"Error reading file: {str(e)}"
    
    def create_backup(self, filename: str = None):
        """Scene'in backup'ını oluşturur"""
        target_file = filename or self.scene.filename
        
        if not target_file:
            return False, "No filename specified"
        
        try:
            backup_filename = f"{target_file}.backup"
            return self.save_scene_to_file(backup_filename)
        except Exception as e:
            return False, f"Backup failed: {str(e)}"
    
    def auto_save(self, auto_save_dir: str = None):
        """Otomatik kaydetme yapar"""
        if not auto_save_dir:
            auto_save_dir = os.path.dirname(self.scene.filename) if self.scene.filename else "."
        
        try:
            # Otomatik kaydetme dosya adı oluştur
            base_name = "autosave"
            if self.scene.filename:
                base_name = f"autosave_{Path(self.scene.filename).stem}"
            
            auto_save_filename = os.path.join(auto_save_dir, f"{base_name}.json")
            return self.save_scene_to_file(auto_save_filename)
            
        except Exception as e:
            dumpException(e)
            return False
