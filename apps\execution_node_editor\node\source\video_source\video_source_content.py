# -*- coding: utf-8 -*-
"""
Video Source Content Widget - <PERSON><PERSON><PERSON> tasar<PERSON>m
"""
import os
from typing import TYPE_CHECKING
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFileDialog
from qtpy.QtCore import Qt, Signal, QSize
from qtpy.QtGui import QFont

from nodeeditor.node_empty_content import QDMEmptyContentWidget
from apps.execution_node_editor.localization import i18n

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class VideoSourceContentWidget(QDMEmptyContentWidget):
    """Video source content widget"""
    
    fileSelected = Signal(str)
    
    def __init__(self, node: 'Node', parent: QWidget = None):
        self.selected_file = ""
        self.file_button = None
        self.info_container = None
        super().__init__(node, parent)
        self.setupUI()
    
    def setupUI(self):
        """UI'ı kur"""
        self.setContentMargins(6, 6, 6, 6)
        self.setContentSpacing(6)
        
        # Dosya seç butonu
        self.file_button = QPushButton(i18n.get('select_video_btn'))
        self.file_button.setToolTip(i18n.get('select_video_btn'))
        self.file_button.setCursor(Qt.PointingHandCursor)
        self.file_button.setMinimumHeight(32)
        self.file_button.setStyleSheet("""
            QPushButton {
                background: rgba(180, 70, 130, 0.9);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(180, 70, 130, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: rgba(150, 50, 100, 1.0);
            }
        """)
        self.file_button.clicked.connect(self.selectFile)
        self.addWidget(self.file_button)
        
        # Dosya bilgi alanı
        self.info_container = QWidget()
        self.info_layout = QVBoxLayout()
        self.info_layout.setContentsMargins(6, 6, 6, 6)
        self.info_layout.setSpacing(2)
        self.info_container.setLayout(self.info_layout)
        self.info_container.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
        """)
        self.info_container.hide()
        self.addWidget(self.info_container)
        
        # Bilgi satırları
        self.name_row = self.createInfoRow(i18n.get('file_name_label'))
        self.size_row = self.createInfoRow(i18n.get('file_size_label'))
        self.type_row = self.createInfoRow(i18n.get('file_type_label'))
        self.duration_row = self.createInfoRow(i18n.get('file_duration_label'))

        # Value label'larını sakla
        self.name_label = self.name_row['value']
        self.size_label = self.size_row['value']
        self.type_label = self.type_row['value']
        self.duration_label = self.duration_row['value']
        
        # Clear butonu
        self.clear_button = QPushButton(i18n.get('clear_selection_btn'))
        self.clear_button.setToolTip(i18n.get('clear_selection_tooltip'))
        self.clear_button.setMaximumHeight(20)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background: rgba(180, 70, 70, 0.8);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
            }
            QPushButton:hover {
                background: rgba(180, 70, 70, 1.0);
            }
            QPushButton:pressed {
                background: rgba(150, 50, 50, 1.0);
            }
        """)
        self.clear_button.clicked.connect(self.clearSelection)
        self.info_layout.addWidget(self.clear_button)
        
        self.addStretch()
        self.setMinimumSize(160, 40)
    
    def createInfoRow(self, label_text: str):
        """Bilgi satırı oluştur"""
        row_widget = QWidget()
        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(0, 0, 0, 0)
        row_layout.setSpacing(6)

        # Label
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 9px;
                font-weight: bold;
            }
        """)
        label.setFixedWidth(55)
        row_layout.addWidget(label)

        # Value
        value_label = QLabel("")
        value_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 9px;
            }
        """)
        value_label.setWordWrap(True)
        row_layout.addWidget(value_label)

        row_widget.setLayout(row_layout)
        self.info_layout.addWidget(row_widget)

        return {
            'widget': row_widget,
            'label': label,
            'value': value_label
        }
    
    def selectFile(self):
        """Video dosyası seç"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Video File", 
            '', 
            "Videos (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm)"
        )
        
        if file_path:
            self.selected_file = file_path
            self.updateFileInfo(file_path)
            
            if self.node:
                if not hasattr(self.node, 'settings'):
                    self.node.settings = {}
                self.node.settings['file_path'] = file_path
            
            self.fileSelected.emit(file_path)
    
    def updateFileInfo(self, file_path: str):
        """Video dosya bilgilerini güncelle"""
        if not file_path or not os.path.exists(file_path):
            self.info_container.hide()
            self.file_button.show()
            return
        
        try:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # Boyut formatla
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            
            # Uzantı
            file_ext = os.path.splitext(filename)[1].upper().replace('.', '')
            if not file_ext:
                file_ext = "Unknown"
            
            # Dosya adını kısalt
            if len(filename) > 25:
                display_name = filename[:22] + "..."
            else:
                display_name = filename
            
            # Bilgileri güncelle
            self.name_label.setText(display_name)
            self.size_label.setText(size_str)
            self.type_label.setText(file_ext)
            self.duration_label.setText("Unknown")  # Video süresini almak için ffmpeg gerekir
            
            self.file_button.hide()
            self.info_container.show()

            # Node boyutunu güncelle
            self.updateNodeGeometry()

        except Exception as e:
            print(f"Error getting video file info: {e}")
            self.info_container.hide()
            self.file_button.show()
            self.updateNodeGeometry()
    
    def clearSelection(self):
        """Seçimi temizle"""
        self.selected_file = ""
        self.info_container.hide()
        self.file_button.show()

        # Node boyutunu güncelle
        self.updateNodeGeometry()

        if self.node and hasattr(self.node, 'settings'):
            self.node.settings.pop('file_path', None)

    def updateNodeGeometry(self):
        """Node geometrisini güncelle"""
        if self.node and hasattr(self.node, 'grNode'):
            from qtpy.QtCore import QTimer
            QTimer.singleShot(10, self._doUpdateGeometry)

    def _doUpdateGeometry(self):
        """Gerçek geometry güncellemesi"""
        if self.node and hasattr(self.node, 'grNode'):
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
            elif hasattr(self.node.grNode, 'updateContentGeometry'):
                self.node.grNode.updateContentGeometry()

            self.updateSocketPositions()

            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle"""
        if self.node:
            for socket in getattr(self.node, 'inputs', []) + getattr(self.node, 'outputs', []):
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        if hasattr(self, 'info_container') and not self.info_container.isHidden():
            base_width = 220
            base_height = 140
        else:
            base_width = 180
            base_height = 45

        return QSize(base_width, base_height)
    
    def updateTexts(self):
        """Metinleri güncelle"""
        if hasattr(self, 'file_button') and self.file_button:
            self.file_button.setText(i18n.get('select_video_btn'))
            self.file_button.setToolTip(i18n.get('select_video_btn'))
        if hasattr(self, 'clear_button') and self.clear_button:
            self.clear_button.setText(i18n.get('clear_selection_btn'))
            self.clear_button.setToolTip(i18n.get('clear_selection_tooltip'))

        # Info row label'larını güncelle
        if hasattr(self, 'name_row'):
            self.name_row['label'].setText(i18n.get('file_name_label'))
        if hasattr(self, 'size_row'):
            self.size_row['label'].setText(i18n.get('file_size_label'))
        if hasattr(self, 'type_row'):
            self.type_row['label'].setText(i18n.get('file_type_label'))
        if hasattr(self, 'duration_row'):
            self.duration_row['label'].setText(i18n.get('file_duration_label'))
    
    def serialize(self):
        """Serialize"""
        data = super().serialize()
        data.update({
            'widget_type': 'VideoSourceContentWidget',
            'selected_file': self.selected_file
        })
        return data
    
    def deserialize(self, data, hashmap={}, restore_id=True):
        """Deserialize"""
        super().deserialize(data, hashmap, restore_id)
        if 'selected_file' in data and data['selected_file']:
            self.selected_file = data['selected_file']
            self.updateFileInfo(self.selected_file)
        return True
