# -*- coding: utf-8 -*-
"""
Dynamic Source Content Widget - Source node'ları için dinamik content widget
"""
from typing import TYPE_CHECKING, Optional, Any
from qtpy.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog, QFrame
from qtpy.QtCore import Qt, Signal, QTimer
from qtpy.QtGui import QPixmap, QIcon

from nodeeditor.node_dynamic_content import QDMDynamicContentWidget
from nodeeditor.node_property_system import PropertyDefinition, PropertyType
from nodeeditor.node_layout_manager import LayoutType

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class DynamicSourceContentWidget(QDMDynamicContentWidget):
    """Source node'ları için dinamik content widget"""
    
    # Signals
    fileSelected = Signal(str)  # Dosya seçildiğinde
    filesSelected = Signal(list)  # Ç<PERSON>lu dosya seçildiğinde
    
    def __init__(self, node: 'Node', source_type: str = "image", parent: QWidget = None):
        """
        :param node: Node referansı
        :param source_type: Source türü ("image", "video", "multi_image")
        :param parent: Parent widget
        """
        self.source_type = source_type
        self.legacy_widget = None
        self.file_button = None
        self.preview_area = None

        super().__init__(node, parent)

        # Property'leri tanımla (super init'ten sonra)
        self.setupSourceProperties()

        # Legacy widget'ı embed et
        self.embedLegacyWidget()

    def setupLegacyLayout(self):
        """Legacy layout sistemini ayarla - override parent method"""
        # Ana layout - minimal padding
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(4, 4, 4, 4)  # Minimal padding
        self.main_layout.setSpacing(2)  # Minimal spacing
        self.setLayout(self.main_layout)

        # Legacy container'ı direkt ekle
        self.createLegacyContainer()
        if hasattr(self, 'legacy_container'):
            self.main_layout.addWidget(self.legacy_container)

        # Property container widget (basit) - sadece gerekirse
        if hasattr(self.node, 'property_manager') and self.node.property_manager._properties:
            self.property_container = QWidget()
            self.property_layout = QVBoxLayout()
            self.property_layout.setContentsMargins(0, 0, 0, 0)
            self.property_layout.setSpacing(2)
            self.property_container.setLayout(self.property_layout)

            self.main_layout.addWidget(self.property_container)

            # Property widget'larını oluştur
            self.createPropertyWidgets()

        # Stretch ekle
        self.main_layout.addStretch()

    def initUI(self):
        """UI'ı başlat - override parent method"""
        # Source widget'lar için özel layout kullan
        self.use_layout_manager = False  # Layout manager'ı devre dışı bırak
        self.setupLegacyLayout()

        # İlk boyut hesaplaması
        self.updateGeometry()
    
    def setupSourceProperties(self):
        """Source türüne göre property'leri ayarla - sadece dosya yolu"""
        if not hasattr(self.node, 'property_manager'):
            return

        # Sadece dosya yolu property'si
        if self.source_type == "image":
            file_property = PropertyDefinition(
                name="file_path",
                prop_type=PropertyType.STRING,
                default_value="",
                display_name="Image File",
                description="Selected image file path"
            )
        elif self.source_type == "video":
            file_property = PropertyDefinition(
                name="file_path",
                prop_type=PropertyType.STRING,
                default_value="",
                display_name="Video File",
                description="Selected video file path"
            )
        elif self.source_type == "multi_image":
            file_property = PropertyDefinition(
                name="file_paths",
                prop_type=PropertyType.STRING,
                default_value="",
                display_name="Image Files",
                description="Selected image files (comma separated)"
            )
        else:
            return

        # Property'yi tanımla
        self.node.property_manager.define_properties([file_property])
    
    def organizePropertiesIntoGroups(self):
        """Property'leri gruplara ayır"""
        if not self.use_layout_manager:
            return
        
        # Source grubu
        source_group = self.layout_manager.addGroup("source", "Source Settings")
        
        # File grubu
        file_group = self.layout_manager.addGroup("file", "File Settings")
        
        # Quality grubu
        quality_group = self.layout_manager.addGroup("quality", "Quality Settings")
        
        # Property'leri gruplara ayır
        for prop_name in self.node.property_manager._properties.keys():
            if prop_name.startswith('node_'):
                source_group.addProperty(prop_name)
            elif 'path' in prop_name:
                file_group.addProperty(prop_name)
            elif 'quality' in prop_name or 'speed' in prop_name:
                quality_group.addProperty(prop_name)
            else:
                source_group.addProperty(prop_name)
    
    def embedLegacyWidget(self):
        """Legacy widget'ı embed et"""
        if not self.use_layout_manager:
            return
        
        # Legacy widget için özel grup oluştur
        legacy_group = self.layout_manager.addGroup("legacy", "File Browser", collapsible=False)
        
        # Legacy widget container oluştur
        self.createLegacyContainer()
        
        # Container'ı layout'a ekle (manuel olarak)
        if hasattr(self.layout_manager, 'main_widget') and self.layout_manager.main_widget:
            main_layout = self.layout_manager.main_widget.layout()
            if main_layout and self.legacy_container:
                main_layout.addWidget(self.legacy_container)
    
    def createLegacyContainer(self):
        """Legacy widget için container oluştur"""
        self.legacy_container = QFrame()
        self.legacy_container.setFrameStyle(QFrame.NoFrame)  # Frame yok
        self.legacy_container.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)  # Margin yok
        layout.setSpacing(2)  # Minimal spacing

        # File selection button (başlık yok)
        self.createFileButton()
        layout.addWidget(self.file_button)

        # Preview area - sadece dosya seçildiğinde göster
        self.preview_area = QLabel("")  # Boş başlat
        self.preview_area.setAlignment(Qt.AlignCenter)
        self.preview_area.setMinimumHeight(0)  # Minimum height yok
        self.preview_area.setStyleSheet("""
            QLabel {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.8);
                font-size: 11px;
            }
        """)
        self.preview_area.hide()  # Başlangıçta gizli
        layout.addWidget(self.preview_area)

        self.legacy_container.setLayout(layout)
    
    def createFileButton(self):
        """File selection button oluştur"""
        if self.source_type == "multi_image":
            button_text = "Select Images"
            button_tip = "Select multiple image files"
        elif self.source_type == "video":
            button_text = "Select Video"
            button_tip = "Select a video file"
        else:
            button_text = "Select Image"
            button_tip = "Select an image file"
        
        self.file_button = QPushButton(button_text)
        self.file_button.setToolTip(button_tip)
        self.file_button.setCursor(Qt.PointingHandCursor)
        self.file_button.setMinimumHeight(24)  # Daha küçük
        self.file_button.setStyleSheet("""
            QPushButton {
                background: rgba(70, 130, 180, 0.8);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
            }
            QPushButton:hover {
                background: rgba(70, 130, 180, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.4);
            }
            QPushButton:pressed {
                background: rgba(50, 100, 150, 1.0);
            }
        """)
        
        # Button click handler
        self.file_button.clicked.connect(self.onFileButtonClicked)
    
    def onFileButtonClicked(self):
        """File button tıklandığında"""
        if self.source_type == "multi_image":
            self.selectMultipleFiles()
        else:
            self.selectSingleFile()
    
    def selectSingleFile(self):
        """Tek dosya seç"""
        if self.source_type == "video":
            file_filter = "Videos (*.mp4 *.avi *.mov *.mkv *.wmv)"
            dialog_title = "Select Video File"
        else:
            file_filter = "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
            dialog_title = "Select Image File"
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, dialog_title, '', file_filter
        )
        
        if file_path:
            # Property'yi güncelle
            prop_name = f"{self.source_type}_path"
            if hasattr(self.node, 'property_manager'):
                self.node.property_manager.set_property(prop_name, file_path)
            
            # Preview'ı güncelle
            self.updatePreview(file_path)
            
            # Signal emit et
            self.fileSelected.emit(file_path)
    
    def selectMultipleFiles(self):
        """Çoklu dosya seç"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Select Image Files", '', "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_paths:
            # Property'yi güncelle (comma separated)
            paths_str = ",".join(file_paths)
            if hasattr(self.node, 'property_manager'):
                self.node.property_manager.set_property("image_paths", paths_str)
            
            # Preview'ı güncelle
            self.updatePreview(file_paths)
            
            # Signal emit et
            self.filesSelected.emit(file_paths)
    
    def updatePreview(self, file_path_or_paths):
        """Preview alanını güncelle"""
        if not self.preview_area:
            return

        if isinstance(file_path_or_paths, list):
            # Çoklu dosya
            count = len(file_path_or_paths)
            if count > 0:
                self.preview_area.setText(f"{count} files")
                self.preview_area.show()
            else:
                self.preview_area.hide()
        else:
            # Tek dosya
            if file_path_or_paths:
                import os
                filename = os.path.basename(file_path_or_paths)
                # Dosya adını kısalt
                if len(filename) > 20:
                    filename = filename[:17] + "..."
                self.preview_area.setText(filename)
                self.preview_area.show()
            else:
                self.preview_area.hide()
    
    def updateGeometry(self):
        """Widget boyutunu güncelle - override parent method"""
        # Basit boyut hesaplama
        total_height = 100  # Minimum height
        total_width = 200   # Minimum width

        # Property container'ın boyutunu hesapla
        if hasattr(self, 'property_container') and self.property_container:
            prop_size = self.property_container.sizeHint()
            total_height += prop_size.height()
            total_width = max(total_width, prop_size.width())

        # Legacy container'ın boyutunu hesapla
        if hasattr(self, 'legacy_container') and self.legacy_container:
            legacy_size = self.legacy_container.sizeHint()
            total_height += legacy_size.height() + 10  # Padding
            total_width = max(total_width, legacy_size.width())

        # Min/max sınırları uygula
        final_width = max(self._min_width, min(total_width, self._max_width))
        final_height = max(self._min_height, min(total_height, self._max_height))

        self.setFixedSize(final_width, final_height)

    def updateTexts(self):
        """Metinleri güncelle - override parent method"""
        # Source widget'lar için özel metin güncelleme
        if hasattr(self, 'file_button') and self.file_button:
            if self.source_type == "multi_image":
                self.file_button.setText("Select Images")
            elif self.source_type == "video":
                self.file_button.setText("Select Video")
            else:
                self.file_button.setText("Select Image")

        # Property widget'larındaki metinleri güncelle
        if hasattr(self, '_property_widgets'):
            for widget in self._property_widgets.values():
                if hasattr(widget, 'updateTexts'):
                    widget.updateTexts()

    def onPropertyChanged(self, property_name: str, old_value, new_value):
        """Property değiştiğinde"""
        super().onPropertyChanged(property_name, old_value, new_value)

        # File path property'leri değiştiğinde preview'ı güncelle
        if property_name.endswith('_path') or property_name == 'image_paths':
            if new_value:
                if property_name == 'image_paths':
                    paths = new_value.split(',') if new_value else []
                    self.updatePreview(paths)
                else:
                    self.updatePreview(new_value)
    
    def serialize(self):
        """Widget'ı serialize et"""
        data = super().serialize()
        data.update({
            'source_type': self.source_type,
            'legacy_widget_type': f'{self.source_type}_source'
        })
        return data
    
    def deserialize(self, data: dict, hashmap: dict = {}, restore_id: bool = True):
        """Widget'ı deserialize et"""
        super().deserialize(data, hashmap, restore_id)
        
        # Source type'ı geri yükle
        if 'source_type' in data:
            self.source_type = data['source_type']
        
        return True
