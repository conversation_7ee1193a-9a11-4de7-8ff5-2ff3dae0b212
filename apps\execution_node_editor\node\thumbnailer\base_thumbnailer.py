from PyQt5.QtWidgets import QGraphicsItem
from PyQt5.QtCore import Qt, QRectF, QTimer, QThreadPool, QRunnable, pyqtSignal, QObject
from PyQt5.QtGui import QPixmap, QImage, QPainter, QColor, QPen, QBrush, QPainterPath
import os
import time

# Signal sınıfı - QRunnable'dan sinyal göndermek için gerekli
class ThumbnailerSignals(QObject):
    """
    Thumbnailer işlemleri için sinyal tanımları
    """
    pixmap_ready = pyqtSignal(QPixmap)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)

# Asenkron işlemler için QRunnable sınıfı
class ThumbnailerWorker(QRunnable):
    """
    Thumbnailer işlemlerini arka planda gerçekleştiren worker sınıfı
    """
    def __init__(self, fn, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = ThumbnailerSignals()
        
    def run(self):
        """
        Worker çalıştırıldığında bu metod işletilir
        """
        try:
            result = self.fn(*self.args, **self.kwargs)
            if isinstance(result, QPixmap):
                self.signals.pixmap_ready.emit(result)
        except Exception as e:
            self.signals.error.emit(str(e))


class BaseThumbnailer(QGraphicsItem):
    """
    Tüm thumbnailer sınıfları için temel sınıf.
    Bu sınıf, ortak özellikleri ve davranışları tanımlar.
    """
    def __init__(self, scene=None, parent=None, parent_node=None):
        super().__init__(parent)
        self.scene = scene
        self.parent_node = parent_node
        
        # parent_node referansını güçlü bir şekilde sakla
        if parent_node:
            self._parent_node_ref = parent_node
            
        # Boyut ve stil ayarları
        self.width = 200
        self.height = 120
        self.border_width = 2
        self.pin_radius = 5
        
        # Pixmap ve başlık
        self.pixmap = None
        self.title = "No Preview"
        
        # Animasyon için değişkenler
        self.animation_offset = 0
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(30)  # 30ms aralıklarla güncelle
        
        # Düğümün başlık rengini al (varsayılan olarak mavi)
        try:
            # Varsayılan renk
            self.border_color = QColor("#3498db")  # Mavi çerçeve
            
            # Öncelikle title_brush'tan rengi almaya çalış
            if self.parent_node and hasattr(self.parent_node, '_brush_title'):
                self.border_color = self.parent_node._brush_title.color()
            # Eğer title_color varsa, onu kullan
            elif self.parent_node and hasattr(self.parent_node, 'title_color'):
                self.border_color = self.parent_node.title_color
        except Exception as e:
            self.border_color = QColor("#3498db")  # Mavi çerçeve
        
        # Stil ayarları
        self._pen_default = QPen(self.border_color)
        self._pen_default.setWidthF(2.0)
        self._brush_background = QBrush(QColor("#2a2a2a"))
        
        # Z değerini düğümün üstünde olacak şekilde ayarla
        self.setZValue(5)
        
        # Thread pool
        self.threadpool = QThreadPool.globalInstance()
        
        # Kalite düşürme oranı (3/2 oranında)
        self.quality_reduction_ratio = 1.5
        
        # Sahneye ekle
        if scene:
            scene.addItem(self)
    
    def boundingRect(self):
        """
        Sınır dikdörtgenini döndürür
        """
        # Pin ve bağlantı çizgisini de içerecek şekilde sınır dikdörtgenini genişlet
        return QRectF(0, 0, self.width, self.height + 90)  # Düğümün altına doğru daha fazla alan
    
    def paint(self, painter, option, widget=None):
        """
        Thumbnailer'ı çizer
        """
        # Önce arkaplanı çiz
        path_title = QPainterPath()
        path_title.setFillRule(Qt.WindingFill)
        path_title.addRoundedRect(0, 0, self.width, self.height, 8, 8)
        painter.setPen(self._pen_default)
        painter.setBrush(self._brush_background)
        painter.drawPath(path_title.simplified())
        
        # Eğer pixmap varsa göster
        if self.pixmap and not self.pixmap.isNull():
            # Görüntüyü ölçeklendir
            scaled_pixmap = self.pixmap.scaled(
                self.width - 10, 
                self.height - 10,
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            # Görüntüyü ortalayarak çiz
            x = (self.width - scaled_pixmap.width()) / 2
            y = (self.height - scaled_pixmap.height()) / 2
            painter.drawPixmap(int(x), int(y), scaled_pixmap)
        else:
            # Metin göster
            painter.setPen(Qt.white)
            painter.drawText(QRectF(0, 0, self.width, self.height), Qt.AlignCenter, self.title)
        
        # Animasyon değeri kullanılarak çizgi çiz
        animation_offset = getattr(self, 'animation_offset', 0)
        dash_length = 6
        gap_length = 3
        total_pattern_length = dash_length + gap_length
        
        # Bağlantı çizgisini düğümün altına doğru çiz
        dash_pen = QPen(self.border_color, 2, Qt.CustomDashLine)  # 2 piksel kalınlık
        
        # Animasyon için dash pattern'i kaydır - ters yönde (yukarıdan aşağıya)
        offset = -animation_offset % total_pattern_length  # Eksi işareti akış yönünü tersine çevirir
        dash_pen.setDashPattern([dash_length, gap_length])
        dash_pen.setDashOffset(offset)  # Animasyon için dash offset'i ayarla
        painter.setPen(dash_pen)
        
        # Çizgiyi pin'den kısa bir şekilde aşağıya uzat
        painter.drawLine(
            int(self.width / 2),                    # başlangıç x
            int(self.height + self.pin_radius),    # başlangıç y - pin'in ortasından başla
            int(self.width / 2),                    # bitiş x
            int(self.height + 45)                   # bitiş y - kısa bir çizgi
        )
        
        # Pin'i çiz - resmin altında, tam ortada
        painter.setPen(QPen(self.border_color, 1))
        painter.setBrush(QBrush(self.border_color))
        painter.drawEllipse(
            int(self.width / 2 - self.pin_radius * 1.2), 
            int(self.height),  # Resmin altında
            int(self.pin_radius * 2.4), 
            int(self.pin_radius * 2.4)
        )
    
    def update_animation(self):
        """
        Animasyon değerini günceller
        """
        # Animasyon değerini sabit hızda artır (akış efekti için)
        self.animation_offset += 0.5  # Daha yavaş ve daha az titreme için küsüratlı artış
        
        # Belirli bir değere ulaşınca sıfırla (döngüsel animasyon)
        if self.animation_offset > 20:
            self.animation_offset = 0
            
        # Yeniden çizim için güncelle
        self.update()
    
    def update_position(self):
        """
        Düğümün konumuna göre Thumbnailer'ı günceller
        """
        if self.parent_node:
            scene_pos = self.parent_node.scenePos()
            node_width = self.parent_node.width
            thumb_x = scene_pos.x() + (node_width - self.width) / 2
            thumb_y = scene_pos.y() - self.height - 35  # Düğümün üstünde göster
            self.setPos(thumb_x, thumb_y)
    
    def set_pixmap(self, pixmap):
        """
        Pixmap'i ayarlar ve görüntüyü günceller
        """
        self.pixmap = pixmap
        self.update()  # Görüntüyü güncellemek için yeniden çiz
    
    def set_title(self, title):
        """
        Başlığı ayarlar
        """
        self.title = title
        self.update()
    
    def set_visible(self, visible):
        """
        Görünürlüğü ayarlar
        """
        self.setVisible(visible)
        if visible:
            self.update_position()
    
    def process_pixmap_async(self, fn, *args, **kwargs):
        """
        Pixmap işlemlerini asenkron olarak gerçekleştirir
        """
        worker = ThumbnailerWorker(fn, *args, **kwargs)
        worker.signals.pixmap_ready.connect(self.set_pixmap)
        worker.signals.error.connect(lambda error: print(f"Thumbnailer error: {error}"))
        self.threadpool.start(worker)
    
    def reduce_image_quality(self, pixmap):
        """
        Görüntü kalitesini 3/2 oranında düşürür
        """
        if pixmap is None or pixmap.isNull():
            return pixmap
            
        # Orijinal boyutları al
        original_width = pixmap.width()
        original_height = pixmap.height()
        
        # 3/2 oranında küçült
        new_width = int(original_width / self.quality_reduction_ratio)
        new_height = int(original_height / self.quality_reduction_ratio)
        
        # Küçültülmüş pixmap oluştur
        reduced_pixmap = pixmap.scaled(
            new_width, 
            new_height, 
            Qt.KeepAspectRatio, 
            Qt.FastTransformation  # Hızlı dönüşüm kullan
        )
        
        return reduced_pixmap
    
    def cleanup(self):
        """
        Kaynakları temizler
        """
        if hasattr(self, 'animation_timer') and self.animation_timer:
            self.animation_timer.stop()
        
        # Sahne referansını temizle
        self.scene = None
        
        # Parent node referansını temizle
        self.parent_node = None
        self._parent_node_ref = None
        
        # Pixmap'i temizle
        self.pixmap = None
        
        # Sahneden kaldır
        if self.scene():
            self.scene().removeItem(self)
    
    # Alt sınıflar tarafından uygulanacak metotlar
    def load_content(self, path):
        """
        İçeriği yükler - alt sınıflar tarafından uygulanmalıdır
        """
        raise NotImplementedError("This method should be implemented by subclasses")
