; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{ADB7AE1D-C23F-4857-9049-3C70E3550E49}
AppName=ExecutionNodeEditor
AppVersion=1.0
;AppVerName=ExecutionNodeEditor 1.0
AppPublisher=<PERSON> Beyer
AppPublisherURL=https://github.com/beyse/NodeEditor
AppSupportURL=https://github.com/beyse/NodeEditor
AppUpdatesURL=https://github.com/beyse/NodeEditor
DefaultDirName={autopf}\ExecutionNodeEditor
ChangesAssociations=yes
DisableProgramGroupPage=yes
LicenseFile=C:\Users\<USER>\source\repos\NodeEditor\apps\execution_node_editor\license.txt
;InfoBeforeFile=C:\Users\<USER>\source\repos\NodeEditor\apps\execution_node_editor\before.txt
;InfoAfterFile=C:\Users\<USER>\source\repos\NodeEditor\apps\execution_node_editor\after.txt
; Remove the following line to run in administrative install mode (install for all users.)
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=C:\Users\<USER>\source\repos\NodeEditor\dist
OutputBaseFilename=Setup ExecutionNodeEditor
SetupIconFile=C:\Users\<USER>\source\repos\NodeEditor\apps\execution_node_editor\assets\icons\installer.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[InstallDelete]
Type: filesandordirs; Name: {app}\*

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "C:\Users\<USER>\source\repos\NodeEditor\dist\ExecutionNodeEditor.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\source\repos\NodeEditor\dist\assets\*"; DestDir: "{app}\assets"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "C:\Users\<USER>\source\repos\NodeEditor\dist\execution_subsystem\*"; DestDir: "{app}\execution_subsystem"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Registry]
Root: HKA; Subkey: "Software\Classes\.nes\OpenWithProgids"; ValueType: string; ValueName: "ExecutionNodeEditorScene.nes"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\ExecutionNodeEditorScene.nes"; ValueType: string; ValueName: ""; ValueData: "ExecutionNodeEditor Scene"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\ExecutionNodeEditorScene.nes\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\ExecutionNodeEditor.exe,0"
Root: HKA; Subkey: "Software\Classes\ExecutionNodeEditorScene.nes\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\ExecutionNodeEditor.exe"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\ExecutionNodeEditor.exe\SupportedTypes"; ValueType: string; ValueName: ".myp"; ValueData: ""

[Icons]
Name: "{autoprograms}\ExecutionNodeEditor"; Filename: "{app}\ExecutionNodeEditor.exe"
Name: "{autodesktop}\ExecutionNodeEditor"; Filename: "{app}\ExecutionNodeEditor.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\ExecutionNodeEditor.exe"; Description: "{cm:LaunchProgram,ExecutionNodeEditor}"; Flags: nowait postinstall skipifsilent

