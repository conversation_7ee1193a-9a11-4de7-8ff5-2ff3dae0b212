# -*- coding: utf-8 -*-
"""
Empty Node Content Widget - <PERSON>mel boş content widget, her node kendi i<PERSON> override eder
"""
from typing import TYPE_CHECKING
from qtpy.QtWidgets import QWidget, QVBoxLayout
from qtpy.QtCore import Qt

from nodeeditor.node_content_widget import QDMNodeContentWidget

if TYPE_CHECKING:
    from nodeeditor.node_node import Node


class QDMEmptyContentWidget(QDMNodeContentWidget):
    """Temel boş content widget - her node kendi içeri<PERSON> override eder"""
    
    def __init__(self, node: 'Node', parent: QWidget = None):
        super().__init__(node, parent)
    
    def initUI(self):
        """UI'ı başlat - varsayılan olarak boş"""
        # Minimal layout
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.setLayout(self.layout)
        
        # Minimum boyut
        self.setMinimumSize(120, 20)
        
        # Şeffaf arka plan
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
    
    def addWidget(self, widget):
        """Widget ekle"""
        if hasattr(self, 'layout'):
            self.layout.addWidget(widget)
    
    def addStretch(self):
        """Stretch ekle"""
        if hasattr(self, 'layout'):
            self.layout.addStretch()
    
    def clearContent(self):
        """İçeriği temizle"""
        if hasattr(self, 'layout'):
            while self.layout.count():
                child = self.layout.takeAt(0)
                if child.widget():
                    child.widget().setParent(None)
    
    def setContentMargins(self, left: int, top: int, right: int, bottom: int):
        """Content margin'larını ayarla"""
        if hasattr(self, 'layout'):
            self.layout.setContentsMargins(left, top, right, bottom)
    
    def setContentSpacing(self, spacing: int):
        """Content spacing'i ayarla"""
        if hasattr(self, 'layout'):
            self.layout.setSpacing(spacing)
    
    def serialize(self):
        """Widget'ı serialize et"""
        return {
            'widget_type': 'QDMEmptyContentWidget'
        }
    
    def deserialize(self, data, hashmap={}, restore_id=True):
        """Widget'ı deserialize et"""
        return True
    
    def updateTexts(self):
        """Metinleri güncelle - override edilebilir"""
        pass
