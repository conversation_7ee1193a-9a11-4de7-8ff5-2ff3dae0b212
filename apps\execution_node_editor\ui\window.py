import sip
from apps.execution_node_editor.core.execution_node_base import GraphicsExecutionNode
from PyQt5 import Qt<PERSON>ore, QtGui
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON>Bar, QWidget, QStyle, QSizePolicy
from qtpy.QtGui import <PERSON>I<PERSON>, Q<PERSON>eySequence
from qtpy.QtWidgets import QMdiArea, QAction, QMessageBox, QFileDialog
from qtpy.QtCore import Qt, Q<PERSON>ignal<PERSON><PERSON>per
from apps.execution_node_editor.version_info import VER<PERSON>ON_<PERSON><PERSON><PERSON>, VERSION_MINOR, VERSION_PATCH
from commit_info import G<PERSON>_HASH

from nodeeditor.node_editor_window import NodeEditorWindow
from apps.execution_node_editor.ui.sub_window import SubWindow
from nodeeditor.utils import dumpException
# Enabling edge validators
from nodeeditor.node_edge import Edge
from nodeeditor.node_edge_validators import (
    edge_validator_debug,
    edge_cannot_connect_two_outputs_or_two_inputs,
    edge_cannot_connect_input_and_output_of_same_node,
    edge_cannot_connect_input_and_output_of_different_type,
    edge_node_connection_rules
)
# Edge validator'ları aktif et
Edge.registerEdgeValidator(edge_cannot_connect_two_outputs_or_two_inputs)
Edge.registerEdgeValidator(edge_cannot_connect_input_and_output_of_same_node)
Edge.registerEdgeValidator(edge_cannot_connect_input_and_output_of_different_type)
Edge.registerEdgeValidator(edge_node_connection_rules)

from apps.execution_node_editor.localization import i18n
from apps.execution_node_editor.ui.menu_manager import MenuManager
from apps.execution_node_editor.ui.dock_manager import NodesDockManager, SettingsDockManager
from apps.execution_node_editor.ui.style_manager import style_manager
from apps.execution_node_editor.ui.update_manager import UpdateManager
from apps.execution_node_editor.ui.process_manager import ProcessManager

# images for the dark skin


DEBUG = False

node_type_definitions = []

# Downloader sınıfları artık UpdateManager'da

class ExecutionNodeEditorWindow(NodeEditorWindow):

    def initUI(self):
        # Manager'ları başlat
        self.update_manager = UpdateManager(self)
        self.process_manager = ProcessManager(self)

        # Fontları yükle
        style_manager.load_fonts()

        # Temel ayarlar
        self.name_company = 'Sebastian Beyer'
        self.name_product = 'ExecutionNodeEditor'
        # self.stylesheet_filename = os.path.join("./assets/qss/nodeeditor-light.qss")
        # loadStylesheets(
        #     "./assets/qss/nodeeditor.qss",
        #     self.stylesheet_filename
        # )

        app_icon = QtGui.QIcon()
        app_icon.addFile('assets/icons/16x16.png', QtCore.QSize(16, 16))
        app_icon.addFile('assets/icons/24x24.png', QtCore.QSize(24, 24))
        app_icon.addFile('assets/icons/32x32.png', QtCore.QSize(32, 32))
        app_icon.addFile('assets/icons/48x48.png', QtCore.QSize(48, 48))
        app_icon.addFile('assets/icons/64x64.png', QtCore.QSize(64, 64))
        app_icon.addFile('assets/icons/128x128.png', QtCore.QSize(128, 128))
        app_icon.addFile('assets/icons/256x256.png', QtCore.QSize(256, 256))
        self.setWindowIcon(app_icon)

        self.empty_icon = QIcon(".")

        if DEBUG:
            # Debug kodu kaldırıldı
            pass  # Kod bloku boş olmamalı
            # pp(CALC_NODES)

        # Execution path artık ProcessManager'da yönetiliyor
    
        self.mdiArea = QMdiArea()
        self.mdiArea.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.mdiArea.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.mdiArea.setViewMode(QMdiArea.SubWindowView)
        self.mdiArea.setDocumentMode(True)
        self.mdiArea.setTabsClosable(False)
        self.mdiArea.setTabsMovable(True)
        self.setCentralWidget(self.mdiArea)

        self.nodeeditor = self.__class__.NodeEditorWidget_class(self)
        self.setCentralWidget(self.nodeeditor)
        # Debug kodu kaldırıldı
        self.nodeeditor.view.addDropListener(self.nodeeditor.onDrop)

        self.mdiArea.subWindowActivated.connect(self.updateMenus)
        self.windowMapper = QSignalMapper(self)
        self.windowMapper.mapped[QWidget].connect(self.setActiveSubWindow)

        self.nodesDockManager = NodesDockManager(self)
        self.settingsDockManager = SettingsDockManager(self)

        self.createActions()
        self.menuManager = MenuManager(self)
        self.createToolBars()
        self.createStatusBar()
        self.updateMenus()
        self.readSettings()
        self.setWindowTitle(i18n.get("app_title"))

        # Stil yöneticisi ile tema uygula
        style_manager.apply_main_window_style(self)
        style_manager.apply_global_styles()

        # Dock ikonları
        self.nodesDockManager.setWindowIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        self.settingsDockManager.setWindowIcon(self.style().standardIcon(QStyle.SP_FileIcon))
        # QMdiArea sekmeleri için Inspector örneği
        for i in range(self.mdiArea.count() if hasattr(self.mdiArea, 'count') else 0):
            self.mdiArea.setTabIcon(i, self.style().standardIcon(QStyle.SP_MessageBoxInformation))

        self.mdiArea.installEventFilter(self)

        i18n.languageChanged.connect(self.updateTranslations)

    def closeEvent(self, event):
        if hasattr(self, "mdiArea") and self.mdiArea is not None and not sip.isdeleted(self.mdiArea):
            self.mdiArea.closeAllSubWindows()
        if hasattr(self, "mdiArea") and self.mdiArea is not None and not sip.isdeleted(self.mdiArea) and self.mdiArea.currentSubWindow():
            event.ignore()
        else:
            self.writeSettings()
            event.accept()
            # hacky fix for PyQt 5.14.x
            import sys
            sys.exit(0)

    def createActions(self):
        super().createActions()

        self.actClose = QAction("Cl&ose", self, statusTip="Close the active window",
                                triggered=self.mdiArea.closeActiveSubWindow)
        self.actCloseAll = QAction(
            "Close &All", self, statusTip="Close all the windows", triggered=self.mdiArea.closeAllSubWindows)
        self.actTile = QAction(
            "&Tile", self, statusTip="Tile the windows", triggered=self.mdiArea.tileSubWindows)
        self.actCascade = QAction(
            "&Cascade", self, statusTip="Cascade the windows", triggered=self.mdiArea.cascadeSubWindows)
        self.actNext = QAction("Ne&xt", self, shortcut=QKeySequence.NextChild,
                               statusTip="Move the focus to the next window", triggered=self.mdiArea.activateNextSubWindow)
        self.actPrevious = QAction("Pre&vious", self, shortcut=QKeySequence.PreviousChild,
                                   statusTip="Move the focus to the previous window", triggered=self.mdiArea.activatePreviousSubWindow)

        self.actSeparator = QAction(self)
        self.actSeparator.setSeparator(True)

        self.actAbout = QAction(
            "&About", self, statusTip="Show the application's About box", triggered=self.about)

        # Güncelleme kontrolü artık UpdateManager'da
        self.update_manager.create_update_action()

    def getCurrentNodeEditorWidget(self):
        """ we're returning NodeEditorWidget here... """
        activeSubWindow = self.mdiArea.activeSubWindow()
        if activeSubWindow:
            return activeSubWindow.widget()
        return None

    def openFile(self, filename):
        self.getCurrentNodeEditorWidget().fileLoad(filename)
        self.setTitle()

    def onFileNew(self):
        try:
            subwnd = self.createMdiChild()
            subwnd.widget().fileNew()
            subwnd.show()
        except Exception as e:
            dumpException(e)

    def onFileOpen(self):
        fnames, filter = QFileDialog.getOpenFileNames(
            self, 'Open graph from file', self.getFileDialogDirectory(), self.getFileDialogFilter())

        try:
            for fname in fnames:
                if fname:
                    existing = self.findMdiChild(fname)
                    if existing:
                        self.mdiArea.setActiveSubWindow(existing)
                    else:
                        # we need to create new subWindow and open the file
                        nodeeditor = SubWindow()
                        if nodeeditor.fileLoad(fname):
                            self.statusBar().showMessage("File %s loaded" % fname, 5000)
                            nodeeditor.setTitle()
                            subwnd = self.createMdiChild(nodeeditor)
                            subwnd.show()
                        else:
                            nodeeditor.close()
        except Exception as e:
            dumpException(e)

    # Güncelleme metodları artık UpdateManager'da
        

    def about(self):
        version_info_text = "{} v{}.{}.{}-{}".format(self.name_product, VERSION_MAJOR, VERSION_MINOR, VERSION_PATCH, GIT_HASH)
        QMessageBox.about(self, "About",
                          version_info_text + "\n\n" +
                          i18n.get("about_text") + "\n\n"
                          "View it on GitHub: https://github.com/beyse/NodeEditor.\n\n"
                          "It is a fork of pyqt-node-editor, created by Pavel Křupala.\n"
                          "Check it out as well at: https://gitlab.com/pavel.krupala/pyqt-node-editor.\n\n"
                          "The project is licensed under the MIT License.\n"
                          "Learn more about it here: https://choosealicense.com/licenses/mit/.")

    def createMenus(self):
        # Artık menü yönetimi MenuManager'da
        pass

    def updateMenus(self):
        # Debug kodu kaldırıldı
        active = self.getCurrentNodeEditorWidget()
        hasMdiChild = (active is not None)

        self.actSave.setEnabled(hasMdiChild)
        self.actSaveAs.setEnabled(hasMdiChild)
        self.actClose.setEnabled(hasMdiChild)
        self.actCloseAll.setEnabled(hasMdiChild)
        self.actTile.setEnabled(hasMdiChild)
        self.actCascade.setEnabled(hasMdiChild)
        self.actNext.setEnabled(hasMdiChild)
        self.actPrevious.setEnabled(hasMdiChild)
        self.actSeparator.setVisible(hasMdiChild)

        self.updateEditMenu()
        self.updateSettingsDock()

    def updateEditMenu(self):
        if not hasattr(self, "mdiArea") or self.mdiArea is None or sip.isdeleted(self.mdiArea):
            return
        try:
            # Debug kodu kaldırıldı
            active = self.getCurrentNodeEditorWidget()
            hasMdiChild = (active is not None)

            self.actPaste.setEnabled(hasMdiChild)

            self.actCut.setEnabled(hasMdiChild and active.hasSelectedItems())
            self.actCopy.setEnabled(hasMdiChild and active.hasSelectedItems())
            self.actDelete.setEnabled(
                hasMdiChild and active.hasSelectedItems())

            self.actUndo.setEnabled(hasMdiChild and active.canUndo())
            self.actRedo.setEnabled(hasMdiChild and active.canRedo())
        except Exception as e:
            dumpException(e)

    def updateSettingsDock(self):
        try:
            # Debug kodu kaldırıldı
            active = self.getCurrentNodeEditorWidget()
            hasMdiChild = (active is not None)

            self.settingsDockManager.update({}, None, False)
            if hasMdiChild:
                selected_items = active.getSelectedItems()
                if len(selected_items) == 1:
                    selected_item = selected_items[0]
                    if isinstance(selected_item, GraphicsExecutionNode):
                        node = selected_item.node
                        self.settingsDockManager.update(node.settings, node.setSettings, True)
        except Exception as e:
            dumpException(e)

    def createToolBars(self):
        toolbar = QToolBar("Main Toolbar")
        toolbar.setFloatable(False)
        toolbar.setMovable(False)
        toolbar.setIconSize(QtCore.QSize(20, 20))

        # Sol spacer
        left_spacer = QWidget()
        left_spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(left_spacer)

        # Play ve Pause butonları
        play_icon = QIcon("apps/execution_node_editor/assets/icons/Editor/play.png")
        pause_icon = QIcon("apps/execution_node_editor/assets/icons/Editor/pause.png")
        self.play_action = QAction(play_icon, "Play", self)
        self.pause_action = QAction(pause_icon, "Pause", self)
        # (İstersen burada self.play_action.triggered.connect(...) ile fonksiyon bağlayabilirsin)
        toolbar.addAction(self.play_action)
        toolbar.addAction(self.pause_action)

        # Sağ spacer
        right_spacer = QWidget()
        right_spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(right_spacer)

        # (İstersen eski run_graph_action'ı kaldırabilirsin veya ayrı bir toolbar'a ekleyebilirsin)
        self.addToolBar(toolbar)

    def run_graph(self):
        """Grafik çalıştırma artık ProcessManager'da"""
        self.process_manager.run_graph()

    def createMdiChild(self, child_widget=None):
        nodeeditor = child_widget if child_widget is not None else SubWindow()
        # Debug kodu kaldırıldı
        nodeeditor.view.addDropListener(nodeeditor.onDrop)
        subwnd = self.mdiArea.addSubWindow(nodeeditor)
        # Sadece ikon, başlık boş
        icon_path = 'apps/execution_node_editor/assets/icons/Editor/NodeEditor.png'
        subwnd.setWindowIcon(QIcon(icon_path))
        subwnd.setWindowTitle("")
        # Çerçevesiz (frameless) pencere: üstteki başlık ve sistem çerçevesi gizlensin
        from PyQt5.QtCore import Qt
        flags = subwnd.windowFlags()
        flags |= Qt.FramelessWindowHint
        subwnd.setWindowFlags(flags)
        subwnd.setStyleSheet(style_manager.get_mdi_subwindow_style())
        subwnd.show()
        subwnd.showMaximized()
        nodeeditor.scene.history.addHistoryModifiedListener(
            self.updateEditMenu)
        nodeeditor.scene.history.addHistoryModifiedListener(
            self.updateSettingsDock)
        nodeeditor.addCloseEventListener(self.onSubWndClose)
        return subwnd

    def onSubWndClose(self, widget, event):
        existing = self.findMdiChild(widget.filename)
        self.mdiArea.setActiveSubWindow(existing)

        if self.maybeSave():
            event.accept()
        else:
            event.ignore()

    def findMdiChild(self, filename):
        for window in self.mdiArea.subWindowList():
            if window.widget().filename == filename:
                return window
        return None

    def setActiveSubWindow(self, window):
        if window:
            self.mdiArea.setActiveSubWindow(window)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if hasattr(self, 'mdiArea'):
            pass

    def eventFilter(self, obj, event):
        if hasattr(self, 'mdiArea') and obj == self.mdiArea and event.type() == QtCore.QEvent.Resize:
            pass
        return super().eventFilter(obj, event)

    def updateTranslations(self):
        self.setWindowTitle(i18n.get("app_title"))
        if hasattr(self, 'menuManager'):
            self.menuManager.updateMenus()
        # Aktif node editor başlığını da güncelle (mdiArea silinmişse erişme)
        if hasattr(self, "mdiArea") and self.mdiArea is not None and not sip.isdeleted(self.mdiArea):
            active = self.getCurrentNodeEditorWidget()
            if active and hasattr(active, 'setTitleBarText'):
                active.setTitleBarText()
        # Dock başlıklarını da güncelle
        if hasattr(self, 'nodesDockManager'):
            self.nodesDockManager.updateTexts()
        if hasattr(self, 'settingsDockManager'):
            self.settingsDockManager.updateTexts()

    def onWindowNodesToolbar(self):
        if self.nodesDockManager.isVisible():
            self.nodesDockManager.hide()
        else:
            self.nodesDockManager.show()

    def contextMenuEvent(self, event):
        # Debug kodu kaldırıldı
        super().contextMenuEvent(event)

# Global stiller artık StyleManager'da uygulanıyor
