"""
G<PERSON><PERSON>lleme yönetimi için ayrı modül
Mevcut window.py'deki güncelleme kodlarını organize eder
"""
import os
import sys
import requests
import shutil
import tempfile
import subprocess
from PyQt5 import QtCore
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QRunnable, QThreadPool
from PyQt5.QtWidgets import QMessageBox, QAction
from apps.execution_node_editor.version_info import VERSION_MAJOR, VERSION_MINOR, VERSION_PATCH


class DownloaderSignals(QObject):
    """Downloader sinyalleri"""
    signalSuccess = QtCore.pyqtSignal()
    installerPath = QtCore.pyqtSignal(object)


class Downloader(QRunnable):
    """Güncelleme dosyasını indiren sınıf"""
    
    def __init__(self):
        super(Downloader, self).__init__()
        self.signals = DownloaderSignals()

    def download_file(self, url, folder_name, local_filename):
        """Dosyayı indirir"""
        path = os.path.join(folder_name, local_filename)
        with requests.get(url, stream=True) as r:
            with open(path, 'wb') as f:
                shutil.copyfileobj(r.raw, f)
        return path

    @pyqtSlot()
    def run(self):
        """İndirme işlemini çalıştırır"""
        try:
            response = requests.get("https://api.github.com/repos/beyse/NodeEditor/releases/latest")
            release_info = response.json()
            assets = release_info["assets"]
            tag_name = release_info["tag_name"]
            url = None
            
            for asset in assets:
                content_type = asset["content_type"]
                name = asset["name"]
                if content_type == "application/x-msdownload" and (name.startswith('Setup') or name.startswith('Install')): 
                    url = asset["browser_download_url"]
            
            if url is not None:
                local_filename = 'install_{}.exe'.format(tag_name)
                tmpdir = tempfile.gettempdir()
                app_tmpdir = os.path.join(tmpdir, 'ExecutionNodeEditor')
                if not os.path.exists(app_tmpdir):
                    os.mkdir(app_tmpdir)
                
                path = self.download_file(url, app_tmpdir, local_filename)
                self.signals.installerPath.emit(path)
                self.signals.signalSuccess.emit()
        except Exception as e:
            pass


class UpdateManager:
    """Güncelleme yönetimini sağlayan sınıf"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.downloader = None
        self.threadpool = None
        self.process = None
        
    def check_for_update(self):
        """Güncelleme olup olmadığını kontrol eder"""
        try:
            response = requests.get("https://api.github.com/repos/beyse/NodeEditor/releases/latest")
            release_info = response.json()
            tag_name = release_info["tag_name"]
            tag_name = tag_name.replace('v', '')
            tokens = tag_name.split('.')
            r_major = int(tokens[0])
            r_minor = int(tokens[1])
            r_patch = int(tokens[2])

            if r_major > VERSION_MAJOR or r_minor > VERSION_MINOR or r_patch > VERSION_PATCH:
                return True
            else:
                return False
        except Exception:
            return False
    
    def create_update_action(self):
        """Güncelleme action'ını oluşturur"""
        if self.check_for_update():
            actUpdate = QAction("&Get Latest Version!", self.main_window, 
                              statusTip="A newer version is available. Click here to download and install it.", 
                              triggered=self.start_update)
            self.main_window.menuBar().addAction(actUpdate)
            return actUpdate
        return None
    
    def start_update(self):
        """Güncelleme işlemini başlatır"""
        self.threadpool = QThreadPool()
        self.downloader = Downloader()
        self.downloader.signals.installerPath.connect(self.update_ready)
        self.threadpool.start(self.downloader)
        self.main_window.statusBar().showMessage("Downloading...", 10000)
        
        # Update action'ını menüden kaldır
        for action in self.main_window.menuBar().actions():
            if action.text() == "&Get Latest Version!":
                self.main_window.menuBar().removeAction(action)
                break
    
    def update_ready(self, installer_path):
        """Güncelleme hazır olduğunda çağrılır"""
        self.downloader = None
        if installer_path is not None:
            res = QMessageBox.information(
                self.main_window, 
                "Update ready", 
                "Download finished.\nThe application will be closed.\n\nUnsaved changes can be saved."
            )
            if res is not None:
                args = [installer_path]
                self.process = subprocess.Popen(args)
                # Ana pencereyi kapat
                if hasattr(self.main_window, 'actExit'):
                    self.main_window.actExit.trigger()
                else:
                    self.main_window.close()
    
    def cleanup(self):
        """Kaynakları temizler"""
        if self.process and self.process.poll() is None:
            self.process.terminate()
        if self.threadpool:
            self.threadpool.clear()
