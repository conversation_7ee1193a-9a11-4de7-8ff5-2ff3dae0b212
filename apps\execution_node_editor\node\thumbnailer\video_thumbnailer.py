from .base_thumbnailer import BaseThumbnailer
from .video_cache import <PERSON>FrameCache, VideoFrameLoader
from .video_controls import VideoControlsProxy
from .video_animation import VideoAnimationManager
import os

class VideoThumbnailer(BaseThumbnailer):
    """
    Video dosyaları için özelleştirilmiş thumbnailer sınıfı.
    Video dosyalarından asenkron olarak kareler çıkarır ve önizleme oluşturur.
    """
    def __init__(self, scene=None, parent=None, parent_node=None):
        super().__init__(scene, parent, parent_node)
        self.title = "Video Preview"
        self.video_path = None

        # Manager'ları başlat
        self.frame_cache = VideoFrameCache(max_cache_size=10)
        self.animation_manager = VideoAnimationManager(self)
        self.controls_proxy = VideoControlsProxy(self)

        # Sinyalleri bağla
        self._connect_signals()

    def _connect_signals(self):
        """Manager sinyallerini bağla"""
        # Animation manager sinyalleri
        self.animation_manager.frame_changed.connect(self._on_frame_changed)

        # Controls sinyalleri
        if self.controls_proxy.controls_widget:
            self.controls_proxy.controls_widget.play_toggled.connect(self._on_play_toggled)
            self.controls_proxy.controls_widget.seek_requested.connect(self._on_seek_requested)
            self.controls_proxy.controls_widget.volume_changed.connect(self._on_volume_changed)

    def _on_frame_changed(self, frame_index):
        """Frame değiştiğinde çağrılır"""
        # Cache'den frame al veya yükle
        pixmap = self.frame_cache.get_frame(frame_index)
        if pixmap is None:
            # Asenkron olarak yükle
            self.process_pixmap_async(self._load_frame_async, frame_index)
        else:
            self.pixmap = pixmap
            self.update()

        # Progress slider'ı güncelle
        if self.controls_proxy.controls_widget:
            position = self.animation_manager.get_current_position()
            self.controls_proxy.controls_widget.set_progress(position)

    def _on_play_toggled(self, is_playing):
        """Play/pause durumu değiştiğinde"""
        if is_playing:
            self.animation_manager.start_animation()
        else:
            self.animation_manager.stop_animation()

    def _on_seek_requested(self, position):
        """Seek işlemi istendiğinde"""
        self.animation_manager.seek_to_position(position)

    def _on_volume_changed(self, volume):
        """Ses seviyesi değiştiğinde"""

    def load_content(self, video_path):
        """Video dosyasını yükler ve önizleme oluşturur"""
        if not video_path or not os.path.exists(video_path):
            self.title = "No Video"
            self.pixmap = None
            self.set_visible(False)
            self.animation_manager.reset()
            return

        self.video_path = video_path
        self.title = os.path.basename(video_path)

        # Video bilgilerini yükle
        video_info = VideoFrameLoader.load_video_info(video_path)
        if video_info:
            self.animation_manager.set_video_info(video_info['frame_count'], video_info['fps'])

            # İlk frame'i asenkron olarak yükle
            self.process_pixmap_async(self._load_first_frame_async, video_path)

            # Kontrolleri göster
            self.controls_proxy.show_controls()
            self.set_visible(True)
            self.update_position()
        else:
            self.title = "Invalid Video"
            self.set_visible(False)

    def _load_first_frame_async(self, video_path):
        """İlk frame'i asenkron olarak yükler"""
        pixmap = VideoFrameLoader.load_first_frame(video_path, self.reduce_image_quality)
        if pixmap:
            self.frame_cache.add_frame(0, pixmap)
        return pixmap

    def _load_frame_async(self, frame_index):
        """Belirli frame'i asenkron olarak yükler"""
        pixmap = VideoFrameLoader.load_frame_at_index(self.video_path, frame_index, self.reduce_image_quality)
        if pixmap:
            self.frame_cache.add_frame(frame_index, pixmap)
        return pixmap
    
    def set_preview_mode(self, mode):
        """Önizleme modunu ayarlar"""
        self.animation_manager.set_preview_mode(mode)

    def refresh(self):
        """Önizlemeyi yeniler"""
        if self.video_path:
            self.load_content(self.video_path)

    def cleanup(self):
        """Kaynakları temizler"""
        # Manager'ları temizle
        if hasattr(self, 'animation_manager'):
            self.animation_manager.cleanup()

        if hasattr(self, 'frame_cache'):
            self.frame_cache.clear()

        if hasattr(self, 'controls_proxy'):
            self.controls_proxy.cleanup()

        # Base sınıfın cleanup metodunu çağır
        super().cleanup()
