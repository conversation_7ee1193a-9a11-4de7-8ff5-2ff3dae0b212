from PyQt5.QtWidgets import QMenu, QLineEdit, QWidgetAction, QHBoxLayout, QLabel, QWidget, QStyle, QProxyStyle
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import QEvent
from apps.execution_node_editor.conf import nodeTypes
from apps.execution_node_editor.localization import i18n

def get_main_window(widget):
    parent = widget
    while parent.parent():
        parent = parent.parent()
    return parent

class ZeroOffsetMenuStyle(QProxyStyle):
    def subElementRect(self, element, option, widget=None):
        rect = super().subElementRect(element, option, widget)
        if element == QStyle.SE_MenuScroller:
            return rect
        if element == QStyle.SE_MenuSubMenuPopup:
            rect.moveLeft(rect.left() - 4)
        return rect

def init_nodes_context_menu(parent_widget):
    main_window = get_main_window(parent_widget)
    context_menu = QMenu(main_window)
    context_menu.setStyle(ZeroOffsetMenuStyle())
    context_menu.setStyleSheet("""
QMenu {
    background-color: #232323;
    color: #e0e0e0;
    border: 1.5px solid #414141;
    border-radius: 8px;
    padding: 12px 8px 12px 8px;
    font-size: 13px;
    font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
}
QMenu::item {
    background: transparent;
    color: #e0e0e0;
    padding: 8px 32px 8px 32px;
    border-radius: 6px;
    margin: 4px 0;
    font-size: 13px;
    font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
}
QMenu::item:selected {
    background: #414141;
    color: #ffcc00;
}
QMenu::separator {
    height: 1px;
    background: #414141;
    margin: 8px 0;
}
QMenu::icon {
    padding-left: 8px;
}
QMenu QMenu {
    border-radius: 8px;
    margin-left: 0px;
    margin-top: 0px;
    border: 1.5px solid #414141;
    background-color: #232323;
}
""")
    # Focus kaybında menüyü kapat
    class FocusCloseMenu(QMenu):
        def event(self, event):
            if event.type() == QEvent.FocusOut:
                self.close()
            return super().event(event)
    context_menu.__class__ = FocusCloseMenu
    search_widget = QWidget(context_menu)
    search_layout = QHBoxLayout()
    search_layout.setContentsMargins(0, 0, 0, 0)
    search_layout.setSpacing(6)
    icon_label = QLabel()
    icon_path = "apps/execution_node_editor/assets/icons/Editor/search.png"
    icon_pixmap = QPixmap(icon_path).scaled(16, 16)
    icon_label.setPixmap(icon_pixmap)
    icon_label.setStyleSheet("padding-left: 4px; padding-right: 2px;")
    search_box = QLineEdit()
    search_box.setPlaceholderText(i18n.get("search_node_placeholder"))
    search_box.setToolTip(i18n.get("search_node_tooltip"))
    search_box.setStyleSheet("background: #313131; color: #e0e0e0; border-radius: 5px; padding: 6px 10px; font-size: 13px;")
    search_layout.addWidget(icon_label)
    search_layout.addWidget(search_box)
    search_widget.setLayout(search_layout)
    search_action = QWidgetAction(context_menu)
    search_action.setDefaultWidget(search_widget)
    context_menu.addAction(search_action)
    def populate_menu(filter_text=""):
        for act in context_menu.actions()[1:]:
            context_menu.removeAction(act)
        keys = list(nodeTypes.keys())
        keys.sort()
        for key in keys:
            sub_menu = QMenu(i18n.get(key), context_menu)
            sub_menu.setStyleSheet(context_menu.styleSheet())
            types = nodeTypes[key]
            added = False
            for type in types:
                if filter_text.lower() in type.lower():
                    action = sub_menu.addAction(i18n.get(type))
                    action.setData(type)
                    added = True
            if added:
                context_menu.addMenu(sub_menu)
    search_box.textChanged.connect(lambda text: populate_menu(text))
    populate_menu()
    return context_menu 