from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt, QTimer
from .base_thumbnailer import BaseThumbnailer
import os

class MultiImageThumbnailer(BaseThumbnailer):
    """
    Çoklu resim dosyaları için özelleştirilmiş thumbnailer sınıfı.
    Birden fazla resim dosyasını asenkron olarak yükler ve aralarında geçiş yaparak önizleme oluşturur.
    """
    def __init__(self, scene=None, parent=None, parent_node=None):
        super().__init__(scene, parent, parent_node)
        self.title = "Multi Image Preview"
        self.image_paths = []
        self.current_index = 0
        
        # Resimler arası geçiş için timer
        self.transition_timer = QTimer()
        self.transition_timer.timeout.connect(self.show_next_image)
        self.transition_timer.setInterval(3000)  # 3 saniyede bir resim değ<PERSON>tir
        
        # Pixmap cache - her resmi tekrar tekrar yüklememek için
        self.pixmap_cache = {}
    
    def load_content(self, image_paths):
        """
        Çoklu resim dosyalarını asenkron olarak yükler ve önizleme oluşturur
        """
        if not image_paths or len(image_paths) == 0:
            self.title = "No Images"
            self.pixmap = None
            self.set_visible(False)
            self.transition_timer.stop()
            return
        
        self.image_paths = image_paths
        self.current_index = 0
        self.pixmap_cache = {}  # Cache'i temizle
        
        # İlk resmi yükle
        self._load_current_image()
        
        # Birden fazla resim varsa geçiş timerını başlat
        if len(image_paths) > 1:
            self.transition_timer.start()
        else:
            self.transition_timer.stop()
        
        self.set_visible(True)
        self.update_position()
    
    def _load_current_image(self):
        """
        Mevcut indeksteki resmi yükler
        """
        if not self.image_paths or self.current_index >= len(self.image_paths):
            return
        
        current_path = self.image_paths[self.current_index]
        
        # Eğer cache'de varsa, direkt kullan
        if current_path in self.pixmap_cache:
            self.set_pixmap(self.pixmap_cache[current_path])
            return
        
        # Yoksa asenkron olarak yükle
        self.title = f"Loading... ({self.current_index + 1}/{len(self.image_paths)})"
        self.process_pixmap_async(self._load_image, current_path)
    
    def _load_image(self, image_path):
        """
        Resim dosyasını yükler ve QPixmap döndürür.
        Bu metod worker thread üzerinde çalışır.
        """
        try:
            # Burada ağır işlemler yapılabilir, ana thread'i bloke etmez
            pixmap = QPixmap(image_path)
            
            # Eğer pixmap geçersizse None döndür
            if pixmap.isNull():
                return None
            
            # Kaliteyi düşür (3/2 oranında)
            reduced_pixmap = self.reduce_image_quality(pixmap)
            
            # Cache'e ekle
            self.pixmap_cache[image_path] = reduced_pixmap
                
            return reduced_pixmap
        except Exception as e:
            return None
    
    def show_next_image(self):
        """
        Bir sonraki resmi gösterir
        """
        if not self.image_paths or len(self.image_paths) <= 1:
            return
        
        # Sonraki indekse geç
        self.current_index = (self.current_index + 1) % len(self.image_paths)
        
        # Resmi yükle
        self._load_current_image()
    
    def show_previous_image(self):
        """
        Bir önceki resmi gösterir
        """
        if not self.image_paths or len(self.image_paths) <= 1:
            return
        
        # Önceki indekse geç
        self.current_index = (self.current_index - 1) % len(self.image_paths)
        
        # Resmi yükle
        self._load_current_image()
    
    def refresh(self):
        """
        Önizlemeyi yeniler
        """
        if self.image_paths:
            self.load_content(self.image_paths)
    
    def cleanup(self):
        """
        Kaynakları temizler
        """
        self.transition_timer.stop()
        self.pixmap_cache.clear()
        
        # Base sınıfın cleanup metodunu çağır
        super().cleanup()
