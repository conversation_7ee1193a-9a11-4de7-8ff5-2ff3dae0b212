# Terminal Log

**Komut:** `python -m apps.execution_node_editor.main`

**<PERSON>şlang<PERSON><PERSON> Zamanı:** 2025-06-24 16:01:56

## Çıktı

```
DEBUG: Validating edge from <Socket #0 ME 19a..120> to <Socket #0 ME 19a..110>
Edge Validation Error: Type check: input=DataType.TRIGGER, output=DataType.TRIGGER
Edge Validation Error: After conversion: input=DataType.TRIGGER, output=DataType.TRIGGER
Edge Validation Error: Type check passed
Edge Validation Error: Connection rule check: source_node=<image_source_label:ConcreteExecutionNode 19a..690>, target_node=<start_node_label:ConcreteExecutionNode 19a..320>
Edge Validation Error: Node types: image_source -> StartNode
Edge Validation Error: Allowed connections for image_source: ['RemoveBackground', 'ImageResizer', 'EndNode']
Edge Validation Error: Connection not allowed: image_source -> StartNode
DEBUG: Edge validation result: False
DEBUG: Edge validation failed
DEBUG: Validating edge from <Socket #0 ME 19a..120> to <Socket #0 ME 19a..110>
Edge Validation Error: Type check: input=DataType.TRIGGER, output=DataType.TRIGGER
Edge Validation Error: After conversion: input=DataType.TRIGGER, output=DataType.TRIGGER
Edge Validation Error: Type check passed
Edge Validation Error: Connection rule check: source_node=<image_source_label:ConcreteExecutionNode 19a..690>, target_node=<start_node_label:ConcreteExecutionNode 19a..320>
Edge Validation Error: Node types: image_source -> StartNode
Edge Validation Error: Allowed connections for image_source: ['RemoveBackground', 'ImageResizer', 'EndNode']
Edge Validation Error: Connection not allowed: image_source -> StartNode
DEBUG: Edge validation result: False
DEBUG: Edge validation failed
```

**Bitiş Zamanı:** 2025-06-24 16:02:11

**Return Code:** 0
