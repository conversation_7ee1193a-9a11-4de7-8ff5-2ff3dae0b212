from PyQt5 import QtGui
from PyQt5.QtWidgets import <PERSON><PERSON>abe<PERSON>
from nodeeditor.utils import dumpException
from nodeeditor.node_socket import SocketDefinition
from apps.execution_node_editor.core.execution_node_base import ExecutionNode, GraphicsExecutionNode
from nodeeditor.node_content_widget import QDMNodeContentWidget
import re

# --- Content Widget Imports ---
from apps.execution_node_editor.node.source import (
    ImageSourceContentWidget,
    VideoSourceContentWidget,
    MultiImageSourceContentWidget,
    DynamicSourceContentWidget
)
from apps.execution_node_editor.node.system import (
    StartNodeContentWidget, EndNodeContentWidget
)

LISTBOX_MIMETYPE = "application/x-item"

OP_NODE_INPUT = 1
OP_NODE_OUTPUT = 2
OP_NODE_ADD = 3
OP_NODE_SUB = 4
OP_NODE_MUL = 5
OP_NODE_DIV = 6


CALC_NODES = {
}

node_counter = {

}

#class NodeTypeDefinition:
#    def __init__(self, type_name, input_sockets, output_sockets):
#        self.type_name = type_name
#        self.input_sockets = input_sockets
#        self.output_sockets = output_sockets


input_sockets = {
    #"ImageSourceNode": [SocketDefinition("int", "imageSourceIn"), SocketDefinition("int", "image source in 2")],
    #"CameraNode": [SocketDefinition("foo", "cameraNode 1")], 
    #"VideNode": [SocketDefinition("foo", "da video")]
}

output_sockets = {
    #"ImageSourceNode": [SocketDefinition("int", "out"), SocketDefinition("int", "oo"), SocketDefinition("int", "oo")],
    #"CameraNode": [SocketDefinition("foo", "hmm 1"), SocketDefinition("a", "hmm 2")], 
    #"VideNode": [SocketDefinition("foo", "oi video")]
}

nodeTypes = {
   
   # "Input": ["ImageSourceNode", "CameraNode", "VideNode"],
    #"Processing": ["AdderNode", "BlurNode", "BinarizeNode"],
    #"Output": ["VideWriterNode"]
} 

defaultSettings = {

}

defaultStyles = {}

node_type_definitions_map = {}

class ConfException(Exception): pass
class InvalidNodeRegistration(ConfException): pass
class OpCodeNotRegistered(ConfException): pass


def register_node_types(node_type_definitions, category = "uncategorized"):
    for d in node_type_definitions:
        if d.node_type not in input_sockets.keys():
            input_sockets[d.node_type] = []
        for i in d.input_ports:
            socket_definition = SocketDefinition(i.data_type, i.port_name, getattr(i, 'label', None))
            input_sockets[d.node_type].append(socket_definition)
        defaultSettings[d.node_type] = d.default_settings
        if d.node_type not in output_sockets.keys():
            output_sockets[d.node_type] = []
        for o in d.output_ports:
            socket_definition = SocketDefinition(o.data_type, o.port_name, getattr(o, 'label', None))
            output_sockets[d.node_type].append(socket_definition)
        if category not in nodeTypes.keys():
            nodeTypes[category] = []
        nodeTypes[category].append(d.node_type)
        if hasattr(d, 'style'):
            defaultStyles[d.node_type] = d.style
        else:
            defaultStyles[d.node_type] = getattr(d, "style", {})
        # NodeTypeDefinition'ı global dict'te sakla
        node_type_definitions_map[d.node_type] = d

class CalcContent(QDMNodeContentWidget):
    def initUI(self):
        from qtpy.QtWidgets import QVBoxLayout
        from qtpy.QtCore import Qt

        # Ana layout oluştur
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(4, 4, 4, 4)
        self.layout.setSpacing(2)
        self.setLayout(self.layout)

    def updateNodeGeometry(self):
        """Node geometrisini güncelle - tüm content widget'lar için ortak"""
        if self.node and hasattr(self.node, 'grNode'):
            # Kısa bir gecikme ile güncelle (UI thread'i bloklamayı önle)
            from qtpy.QtCore import QTimer
            QTimer.singleShot(10, self._doUpdateGeometry)

    def _doUpdateGeometry(self):
        """Gerçek geometry güncellemesi - tüm content widget'lar için ortak"""
        if self.node and hasattr(self.node, 'grNode'):
            # Node'un graphics node'una boyut güncellemesi gönder
            if hasattr(self.node.grNode, 'updateGeometry'):
                self.node.grNode.updateGeometry()
            elif hasattr(self.node.grNode, 'updateContentGeometry'):
                self.node.grNode.updateContentGeometry()

            # Socket pozisyonlarını güncelle
            self.updateSocketPositions()

            # Scene'i güncelle
            if hasattr(self.node, 'scene') and self.node.scene:
                self.node.scene.grScene.update()

    def updateSocketPositions(self):
        """Socket pozisyonlarını güncelle - tüm content widget'lar için ortak"""
        if self.node:
            # Tüm socket'ların pozisyonlarını güncelle
            for socket in getattr(self.node, 'inputs', []) + getattr(self.node, 'outputs', []):
                if hasattr(socket, 'setSocketPosition'):
                    socket.setSocketPosition()

            # Bağlı edge'leri güncelle
            if hasattr(self.node, 'updateConnectedEdges'):
                self.node.updateConnectedEdges()

        # Label oluştur
        from qtpy.QtCore import Qt
        if hasattr(self.node, 'content_label') and self.node.content_label:
            self.wdg_label = QLabel(self.node.content_label, self)
            self.wdg_label.setObjectName(getattr(self.node, 'content_label_objname', 'calc_node_label'))
            self.wdg_label.setAlignment(Qt.AlignCenter)
            self.wdg_label.setWordWrap(True)
            self.layout.addWidget(self.wdg_label)
        else:
            # Boş label oluştur (uyumluluk için)
            self.wdg_label = QLabel("", self)
            self.wdg_label.setObjectName('calc_node_label')
            self.wdg_label.setAlignment(Qt.AlignCenter)
            self.layout.addWidget(self.wdg_label)

        # Minimum boyut ayarla
        self.setMinimumSize(120, 30)

    def updateTexts(self):
        """Metinleri güncelle - CalcContent için override"""
        if hasattr(self, 'wdg_label') and self.wdg_label:
            # Label varsa güncelle
            if hasattr(self.node, 'content_label') and self.node.content_label:
                from apps.execution_node_editor.localization import i18n
                self.wdg_label.setText(i18n.get(self.node.content_label, self.node.content_label))

    def sizeHint(self):
        """Widget'ın tercih ettiği boyutu döndür"""
        from qtpy.QtCore import QSize

        if hasattr(self, 'wdg_label') and self.wdg_label.text():
            # Label'ın boyutuna göre hesapla
            label_size = self.wdg_label.sizeHint()
            width = max(120, label_size.width() + 8)  # Padding ekle
            height = max(30, label_size.height() + 8)
            return QSize(width, height)
        else:
            return QSize(120, 30)  # Varsayılan boyut

def camel_to_snake(name):
  name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
  return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()

def remove_underline(name):
    return name.replace('_', ' ')

def capitalize_each_word(original_str):
    result = ""
    # Split the string and get all words in a list
    list_of_words = original_str.split()
    # Iterate over all elements in list
    for elem in list_of_words:
        # capitalize first letter of each word and add to a string
        if len(result) > 0:
            result = result + " " + elem.strip().capitalize()
        else:
            result = elem.capitalize()
    # If result is still empty then return original string else returned capitalized.
    if not result:
        return original_str
    else:
        return result

class ConcreteExecutionNode(ExecutionNode):
    op_code = OP_NODE_INPUT
    content_label_objname = "calc_node_input"

    def __init__(self, scene, node_type):
        from apps.execution_node_editor.conf import node_type_definitions_map
        self._node_type = node_type
        self.node_type = node_type
        self.settings = defaultSettings[node_type]
        self.style = defaultStyles[node_type]
        self._title_to_set = node_type_definitions_map[node_type].title
        self.op_title = self._title_to_set
        super().__init__(scene, self._title_to_set, inputs=input_sockets[node_type], outputs=output_sockets[node_type], node_type=node_type)
        # Pin label'ları için (eski zincir korunuyor)
        from apps.execution_node_editor.core.node_type_definition import NodeTypeDefinition
        import apps.execution_node_editor.core.node_type_definition as ntd_mod
        for sock in getattr(self, 'inputs', []):
            sock.node = self
            sock.node.style = self.style
            if hasattr(sock, 'port_name'):
                for cat, defs in getattr(ntd_mod, 'categorized_node_type_definitions', {}).items():
                    for d in defs:
                        if d.node_type == node_type:
                            for pd in d.input_ports:
                                if pd.port_name == sock.socket_name:
                                    sock.label = getattr(pd, 'label', sock.socket_name)
            if hasattr(sock, 'grSocket'):
                sock.grSocket.initAssets()
        for sock in getattr(self, 'outputs', []):
            sock.node = self
            sock.node.style = self.style
            if hasattr(sock, 'port_name'):
                for cat, defs in getattr(ntd_mod, 'categorized_node_type_definitions', {}).items():
                    for d in defs:
                        if d.node_type == node_type:
                            for pd in d.output_ports:
                                if pd.port_name == sock.socket_name:
                                    sock.label = getattr(pd, 'label', sock.socket_name)
            if hasattr(sock, 'grSocket'):
                sock.grSocket.initAssets()
        self.eval()

    def onDoubleClicked(self, event):
        pass

    def setSettings(self, s):
        self.settings = s

    def remove(self):
        """Override remove method to prevent deletion of system nodes"""
        # System node'ları (Start/End) silinemez
        if hasattr(self, 'settings') and self.settings.get('is_system_node', False):
            return  # Silme işlemini engelle

        # Normal node'lar için parent metodunu çağır
        super().remove()


    def initInnerClasses(self):
        # Temiz content widget sistemi kullan
        if self.node_type == 'image_source':
            self.content = ImageSourceContentWidget(self)
        elif self.node_type == 'video_source':
            self.content = VideoSourceContentWidget(self)
        elif self.node_type == 'multi_image_source':
            self.content = MultiImageSourceContentWidget(self)
        elif self.node_type == 'StartNode':
            self.content = StartNodeContentWidget(self)
        elif self.node_type == 'EndNode':
            self.content = EndNodeContentWidget(self)
        else:
            self.content = CalcContent(self)
        self.grNode = GraphicsExecutionNode(self)
        max_sockets = max(len(input_sockets[self.node_type]), len(output_sockets[self.node_type]))
        self.grNode.initSizes(max_sockets)
        self.grNode.initAssets()
        self.title = getattr(self, '_title_to_set', self.node_type)

    def serialize(self):
        res = super().serialize()
        res['node_settings'] = self.settings
        return res

    def deserialize(self, data, hashmap={}, restore_id=True, keep_title=False):
        title_before = self.title
        res = super().deserialize(data, hashmap, restore_id)
        self.settings = data['node_settings']
        if keep_title:
            self.title = title_before
        return res


def create_node(scene, node_type):
    try:
        node = ConcreteExecutionNode(scene, node_type)
        return node
    except Exception as e:
        import traceback; traceback.print_exc()
        return None
