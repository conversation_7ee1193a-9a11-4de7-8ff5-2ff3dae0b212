from PIL import Image
import os
import argparse # <PERSON><PERSON><PERSON><PERSON> iş<PERSON><PERSON> i<PERSON> e<PERSON>

def resize_atlas(input_path, target_sprite_width, target_sprite_height, original_sprite_width, original_sprite_height, output_path):
    """
    Bir sprite atlasını, içindeki sprite'ların hedef boyuta gelmesini sağlayacak
    şekilde yeniden boyutlandırır.

    Args:
        input_path (str): <PERSON><PERSON>den boyutlandırılacak atlas dosyasının yolu.
        target_sprite_width (int): Atlas içindeki her bir sprite'ın hedef geni<PERSON>ği.
        target_sprite_height (int): Atlas içindeki her bir sprite'ın hedef yü<PERSON>.
        original_sprite_width (int): Atlastaki sprite'ların orijinal genişliği.
        original_sprite_height (int): Atlastaki sprite'ların orijinal yüksekliği.
        output_path (str): <PERSON><PERSON><PERSON> ka<PERSON>ği PNG dosyasının yolu.
    """
    if original_sprite_width <= 0 or original_sprite_height <= 0:
        return False
    if target_sprite_width <= 0 or target_sprite_height <= 0:
        return False

    try:
        # Çıktı klasörünün var olduğundan emin ol
        output_folder = os.path.dirname(output_path)
        if output_folder and not os.path.exists(output_folder):
            try:
                os.makedirs(output_folder)
            except OSError as e:
                return False

        with Image.open(input_path).convert("RGBA") as img:
            original_atlas_width, original_atlas_height = img.size

            if original_sprite_width == 0: # original_sprite_height == 0 da kontrol edilebilir
                 return False
            
            # Genişlik ve yükseklik için ayrı ölçekleme çarpanları (oranlar korunmayabilir)
            # Eğer oranların korunması isteniyorsa, tek bir multiplier hesaplanmalı
            # ve diğer boyut ona göre ayarlanmalı. Şimdilik GUI'den gelen değerleri kullanalım.
            # Ancak, atlasın genel boyutunu sprite boyutuna göre ölçeklemek daha mantıklı.
            # Genişliğe göre ölçekleyelim, yükseklik de aynı oranda değişsin.
            
            width_multiplier = target_sprite_width / original_sprite_width
            # height_multiplier = target_sprite_height / original_sprite_height # Eğer oranlar korunmayacaksa
            
            # Oranları koruyarak ölçekleme:
            # Sadece genişlik çarpanını kullan, yükseklik de aynı oranda değişsin.
            # Veya daha küçük olan çarpana göre ölçekle ki resim taşmasın.
            # Şimdilik genişlik bazlı ölçekleme yapalım.
            

            new_atlas_width = int(round(original_atlas_width * width_multiplier))
            # Yüksekliği de aynı çarpanla ölçekle (oranları korumak için)
            new_atlas_height = int(round(original_atlas_height * width_multiplier)) 
            
            # Eğer hedef sprite yüksekliği de önemliyse ve oranlar bozulacaksa,
            # o zaman new_atlas_height'ı target_sprite_height'a göre de hesaplamak gerekebilir.
            # Ama bu genellikle istenmez. Genelde tek bir boyuta göre ölçeklenir.

            new_atlas_width = max(1, new_atlas_width)
            new_atlas_height = max(1, new_atlas_height)

            resized_img = img.resize((new_atlas_width, new_atlas_height), Image.Resampling.LANCZOS)
            resized_img.save(output_path, 'PNG')
        
        return True

    except FileNotFoundError:
        return False
    except Exception as e:
        return False

# --- Komut Satırı Argüman İşleme ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bir sprite atlasını, içindeki sprite'ların hedef boyuta ulaşmasını sağlayacak şekilde yeniden boyutlandırır.")
    parser.add_argument("input_atlas", help="Yeniden boyutlandırılacak atlas dosyasının yolu (*_atlas.png).")
    parser.add_argument("target_width", type=int, help="Her bir sprite için hedef genişlik (piksel).")
    parser.add_argument("target_height", type=int, help="Her bir sprite için hedef yükseklik (piksel).")
    parser.add_argument("original_width", type=int, help="Atlastaki sprite'ların orijinal genişliği (piksel).")
    parser.add_argument("original_height", type=int, help="Atlastaki sprite'ların orijinal yüksekliği (piksel).")
    parser.add_argument("-o", "--output", help="Çıktı dosyasının adı (isteğe bağlı). Belirtilmezse girdi adından türetilir.")

    args = parser.parse_args()

    # Çıktı dosya adını belirle
    if args.output:
        output_file = args.output
    else:
        base, ext = os.path.splitext(args.input_atlas)
        # Eğer girdi '_atlas' ile bitiyorsa, onu değiştir, değilse sona ekle
        if base.endswith('_atlas'):
            base = base[:-len('_atlas')]
        output_file = f"{base}_{args.target_width}x{args.target_height}{ext}"
        # Eğer çıktı adı girdi adıyla aynıysa, üzerine yazmayı önlemek için değiştir
        if output_file == args.input_atlas:
             output_file = f"{base}_{args.target_width}x{args.target_height}_resized{ext}"



    # Ana yeniden boyutlandırma fonksiyonunu çağır
    resize_atlas(
        args.input_atlas,
        args.target_width,
        args.target_height,
        args.original_width,
        args.original_height,
        output_file
    )
