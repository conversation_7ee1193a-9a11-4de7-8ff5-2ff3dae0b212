# -*- coding: utf-8 -*-
"""
A module containing NodeE<PERSON>or's class for representing Socket and Socket Position Constants.
"""
from collections import OrderedDict
from nodeeditor.node_serializable import Serializable
from nodeeditor.node_graphics_socket import QDMGraphicsSocket
from typing import TYPE_CHECKING
from apps.execution_node_editor.core.data_types import DataType
if TYPE_CHECKING:
    from nodeeditor.node_node import Node
    from nodeeditor.node_edge import Edge


LEFT_TOP = 1        #:
LEFT_CENTER =2      #:
LEFT_BOTTOM = 3     #:
RIGHT_TOP = 4       #:
RIGHT_CENTER = 5    #:
RIGHT_BOTTOM = 6    #:


DEBUG = True
DEBUG_REMOVE_WARNINGS = False

class SocketDefinition(Serializable):
    def __init__(self, data_type, port_name, label=None):
        # data_type artık DataType enum'u olmalı, ama string de kabul edilebilir (uyumluluk için)
        if isinstance(data_type, str):
            try:
                data_type = DataType(data_type)
            except ValueError:
                pass  # Enum'da yoksa string olarak bırak
        self.data_type = data_type
        self.port_name = port_name
        self.label = label if label is not None else port_name

    def serialize(self):
        return OrderedDict([
            ('data_type', str(self.data_type)),
            ('port_name', self.port_name),
            ('label', self.label),
        ])

    def deserialize(self, data, hashmap={}, restore_id=True):
        self.data_type = data['data_type']
        self.port_name = data['port_name']
        self.label = data.get('label', self.port_name)
        return True

class Socket(Serializable):
    Socket_GR_Class = QDMGraphicsSocket

    """Class representing Socket."""

    def __init__(self, node: 'Node', index: int=0, position: int=LEFT_TOP, socket_name: str = 'unnamed', socket_type: str = 'null', multi_edges: bool=True,
                 count_on_this_node_side: int=1, is_input: bool=False, label: str = None):
        """
        :param node: reference to the :class:`~nodeeditor.node_node.Node` containing this `Socket`
        :type node: :class:`~nodeeditor.node_node.Node`
        :param index: Current index of this socket in the position
        :type index: ``int``
        :param position: Socket position. See :ref:`socket-position-constants`
        :param socket_name: String defining name of this socket
        :param socket_type: String defining type of this socket
        :param multi_edges: Can this socket have multiple `Edges` connected?
        :type multi_edges: ``bool``
        :param count_on_this_node_side: number of total sockets on this position
        :type count_on_this_node_side: ``int``
        :param is_input: Is this an input `Socket`?
        :type is_input: ``bool``
        :param label: String defining label of this socket
        :type label: ``str``

        :Instance Attributes:

            - **node** - reference to the :class:`~nodeeditor.node_node.Node` containing this `Socket`
            - **edges** - list of `Edges` connected to this `Socket`
            - **grSocket** - reference to the :class:`~nodeeditor.node_graphics_socket.QDMGraphicsSocket`
            - **position** - Socket position. See :ref:`socket-position-constants`
            - **index** - Current index of this socket in the position
            - **socket_name** - String defining name of this socket
            - **socket_type** - String defining type of this socket
            - **count_on_this_node_side** - number of sockets on this position
            - **is_multi_edges** - ``True`` if `Socket` can contain multiple `Edges`
            - **is_input** - ``True`` if this socket serves for Input
            - **is_output** - ``True`` if this socket serves for Output
            - **label** - String defining label of this socket
        """
        super().__init__()

        self.node = node
        self.position = position
        self.index = index
        self.socket_name = socket_name
        self.socket_type = socket_type
        self.count_on_this_node_side = count_on_this_node_side
        self.is_multi_edges = multi_edges
        self.is_input = is_input
        self.is_output = not self.is_input
        self.is_valid = True
        self.label = label if label is not None else socket_name


        if DEBUG: print(f"Socket -- creating with index={self.index}, position={self.position}, socket_type={self.socket_type}, is_input={self.is_input}, for node={self.node.node_type if hasattr(self.node, 'node_type') else 'unknown'}")


        self.grSocket = self.__class__.Socket_GR_Class(self)

        self.setSocketPosition()

        self.edges = []

    def __str__(self):
        return "<Socket #%d %s %s..%s>" % (
            self.index, "ME" if self.is_multi_edges else "SE", hex(id(self))[2:5], hex(id(self))[-3:]
        )

    def delete(self):
        """Delete this `Socket` from graphics scene for sure"""
        self.grSocket.setParentItem(None)
        self.node.scene.grScene.removeItem(self.grSocket)
        del self.grSocket

    def changeSocketType(self, new_socket_type: int) -> bool:
        """
        Change the Socket Type

        :param new_socket_type: new socket type
        :type new_socket_type: ``int``
        :return: Returns ``True`` if the socket type was actually changed
        :rtype: ``bool``
        """
        if self.socket_type != new_socket_type:
            self.socket_type = new_socket_type
            self.grSocket.changeSocketType()
            return True
        return False

    def setSocketPosition(self):
        self.grSocket.setPos(*self.node.getSocketPosition(self.index, self.position, self.count_on_this_node_side))

    def serialize(self):
        return OrderedDict([
            ('id', self.id),
            ('index', self.index),
            ('multi_edges', self.is_multi_edges),
            ('position', self.position),
            ('socket_type', str(self.socket_type)),
            ('label', self.label),
        ])

    def deserialize(self, data, hashmap={}, restore_id=True):
        if restore_id: self.id = data['id']
        self.is_multi_edges = data.get('multi_edges', True)
        self.position = data['position']
        self.index = data['index']
        self.socket_type = data['socket_type']
        self.label = data.get('label', None)
        hashmap[self.id] = self
        return True

    def addEdge(self, edge):
        if edge not in self.edges:
            self.edges.append(edge)

    def removeEdge(self, edge):
        if edge in self.edges:
            self.edges.remove(edge)

    def getSocketPosition(self):
        return [self.grSocket.pos().x(), self.grSocket.pos().y()]

    def isConnected(self, edge):
        return edge in self.edges