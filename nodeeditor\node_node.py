"""
A module containing <PERSON><PERSON><PERSON><PERSON><PERSON>'s class for representing `Node`.
"""
from collections import OrderedDict
from nodeeditor.node_graphics_node import QDMGraphicsNode
from nodeeditor.node_content_widget import QDMNodeContentWidget
from nodeeditor.node_serializable import Serializable
from nodeeditor.node_socket import Socket, LEFT_BOTTOM, LEFT_CENTER, LEFT_TOP, RIGHT_BOTTOM, RIGHT_CENTER, RIGHT_TOP, SocketDefinition
from nodeeditor.node_property_system import PropertyManager, PropertyDefinition, PropertyType
from nodeeditor.node_geometry_observer import getGeometryManager
from nodeeditor.node_socket_manager import NodeSocketManager
from nodeeditor.utils import dumpException, pp
from typing import List, Tuple, TYPE_CHECKING
from apps.execution_node_editor.core.data_types import DataType
if TYPE_CHECKING:
    from nodeeditor.node_scene import Scene
    from nodeeditor.node_edge import Edge

DEBUG = False


class Node(Serializable):
    """
    Class representing `Node` in the `Scene`.
    """
    GraphicsNode_class = QDMGraphicsNode
    NodeContent_class = QDMNodeContentWidget
    Socket_class = Socket

    def __init__(self, scene: 'Scene', title: str="No Title", inputs: list=[], outputs: list=[], node_type=None):
        """

        :param scene: reference to the :class:`~nodeeditor.node_scene.Scene`
        :type scene: :class:`~nodeeditor.node_scene.Scene`
        :param title: Node Title shown in Scene
        :type title: str
        :param inputs: list of :class:`~nodeeditor.node_socket.Socket` types from which the `Sockets` will be auto created
        :param outputs: list of :class:`~nodeeditor.node_socket.Socket` types from which the `Sockets` will be auto created
        :param node_type: Type of the node
        :type node_type: str or None

        :Instance Attributes:

            - **scene** - reference to the :class:`~nodeeditor.node_scene.Scene`
            - **grNode** - Instance of :class:`~nodeeditor.node_graphics_node.QDMGraphicsNode` handling graphical representation in the ``QGraphicsScene``. Automatically created in the constructor
            - **content** - Instance of :class:`~nodeeditor.node_graphics_content.QDMGraphicsContent` which is child of ``QWidget`` representing container for all inner widgets inside of the Node. Automatically created in the constructor
            - **inputs** - list containin Input :class:`~nodeeditor.node_socket.Socket` instances
            - **outputs** - list containin Output :class:`~nodeeditor.node_socket.Socket` instances
            - **property_manager** - Property management system

        """
        super().__init__()
        self._title = title
        self.scene = scene
        self._node_type = node_type

        # Property system
        self.property_manager = PropertyManager()
        self.property_manager.propertyChanged.connect(self.onPropertyChanged)
        self.property_manager.propertiesChanged.connect(self.onPropertiesChanged)

        # Geometry management system
        self.geometry_manager = getGeometryManager()

        # Socket management system
        self.socket_manager = NodeSocketManager(self)

        # just to be sure, init these variables
        self.content = None
        self.grNode = None

        # Initialize property definitions before inner classes
        self.initPropertyDefinitions()

        self.initInnerClasses()
        self.initSettings()

        self.title = title

        self.scene.addNode(self)
        self.scene.grScene.addItem(self.grNode)

        # create socket for inputs and outputs
        self.inputs = []
        self.outputs = []
        self.initSockets(inputs, outputs)

        # dirty and evaluation
        self._is_dirty = False
        self._is_invalid = False

    def __str__(self):
        return "<%s:%s %s..%s>" % (self.title, self.__class__.__name__,hex(id(self))[2:5], hex(id(self))[-3:])

    @property
    def node_type(self):
        return self._node_type

    @node_type.setter
    def node_type(self, value):
        self._node_type = value

    @property
    def title(self):
        """
        Title shown in the scene

        :getter: return current Node title
        :setter: sets Node title and passes it to Graphics Node class
        :type: ``str``
        """
        return self._title

    @title.setter
    def title(self, value):
        self._title = value
        if hasattr(self, 'grNode') and self.grNode:
            self.grNode.title = value

    @property
    def pos(self):
        """
        Retrieve Node's position in the Scene

        :return: Node position
        :rtype: ``QPointF``
        """
        return self.grNode.pos()        # QPointF

    def setPos(self, x: float, y: float):
        """
        Sets position of the Graphics Node

        :param x: X `Scene` position
        :param y: Y `Scene` position
        """
        self.grNode.setPos(x, y)


    def initInnerClasses(self):
        """Sets up graphics Node (PyQt) and Content Widget"""
        node_content_class = self.getNodeContentClass()
        graphics_node_class = self.getGraphicsNodeClass()
        if node_content_class is not None: self.content = node_content_class(self)
        if graphics_node_class is not None: self.grNode = graphics_node_class(self)

    def getNodeContentClass(self):
        """Returns class representing nodeeditor content"""
        return self.__class__.NodeContent_class

    def getGraphicsNodeClass(self):
        return self.__class__.GraphicsNode_class

    def initSettings(self):
        """Initialize properties and socket information"""
        self.socket_spacing = 32

        self.input_socket_position = LEFT_BOTTOM
        self.output_socket_position = RIGHT_TOP
        self.input_multi_edged = False
        self.output_multi_edged = True
        self.socket_offsets = {
            LEFT_BOTTOM: -1,
            LEFT_CENTER: -1,
            LEFT_TOP: -1,
            RIGHT_BOTTOM: 1,
            RIGHT_CENTER: 1,
            RIGHT_TOP: 1,
        }

    def initPropertyDefinitions(self):
        """Initialize property definitions - override in subclasses"""
        # Base properties that all nodes have
        base_properties = [
            PropertyDefinition(
                name="node_title",
                prop_type=PropertyType.STRING,
                default_value=self._title,
                display_name="Title",
                description="Node title"
            ),
            PropertyDefinition(
                name="node_enabled",
                prop_type=PropertyType.BOOLEAN,
                default_value=True,
                display_name="Enabled",
                description="Enable/disable this node"
            )
        ]
        self.property_manager.define_properties(base_properties)

    def onPropertyChanged(self, property_name: str, old_value, new_value):
        """Property değiştiğinde çağrılır"""
        if property_name == "node_title":
            self.title = new_value
            if self.grNode:
                self.grNode.title = new_value
        elif property_name == "node_enabled":
            # Node enable/disable logic
            pass

        # Content widget'ı güncelle
        if self.content and hasattr(self.content, 'onPropertyChanged'):
            self.content.onPropertyChanged(property_name, old_value, new_value)

        # Graphics node'u güncelle
        if self.grNode and hasattr(self.grNode, 'onPropertyChanged'):
            self.grNode.onPropertyChanged(property_name, old_value, new_value)

        # Mark dirty for evaluation
        self.markDirty()

    def onPropertiesChanged(self):
        """Herhangi bir property değiştiğinde çağrılır"""
        # Content widget boyutunu yeniden hesapla
        if self.content and hasattr(self.content, 'updateGeometry'):
            self.content.updateGeometry()

        # Graphics node boyutunu yeniden hesapla
        if self.grNode and hasattr(self.grNode, 'updateGeometry'):
            self.grNode.updateGeometry()

    def getProperty(self, name: str):
        """Property değerini al"""
        return self.property_manager.get_property(name)

    def setProperty(self, name: str, value):
        """Property değerini ayarla"""
        self.property_manager.set_property(name, value)

    def addProperty(self, definition: PropertyDefinition):
        """Yeni property ekle"""
        self.property_manager.define_property(definition)

    def removeProperty(self, name: str):
        """Property'yi kaldır (implementation needed)"""
        # TODO: PropertyManager'a remove metodu eklenecek
        pass

    # Socket management methods
    def addInputSocket(self, name: str, socket_type=DataType.STRING, label: str = None, required: bool = False):
        """Giriş socket'i ekle"""
        from nodeeditor.node_socket_manager import SocketFactory
        definition = SocketFactory.create_input_socket(self, name, socket_type, label, required)
        return self.socket_manager.add_socket(definition)

    def addOutputSocket(self, name: str, socket_type=DataType.STRING, label: str = None):
        """Çıkış socket'i ekle"""
        from nodeeditor.node_socket_manager import SocketFactory
        definition = SocketFactory.create_output_socket(self, name, socket_type, label)
        return self.socket_manager.add_socket(definition)

    def removeSocket(self, socket_id: str):
        """Socket'i kaldır"""
        return self.socket_manager.remove_socket(socket_id)

    def getSocketByName(self, name: str):
        """İsme göre socket al"""
        return self.socket_manager.get_socket_by_name(name)

    def getSocketsByType(self, socket_type: DataType, is_input: bool = None):
        """Türe göre socket'ları al"""
        return self.socket_manager.get_sockets_by_type(socket_type, is_input)

    def initSockets(self, inputs: list, outputs: list, reset: bool=True):
        """
        Create sockets for inputs and outputs

        :param inputs: list of Socket Types (int)
        :type inputs: ``list``
        :param outputs: list of Socket Types (int)
        :type outputs: ``list``
        :param reset: if ``True`` destroys and removes old `Sockets`
        :type reset: ``bool``
        """

        if reset:
            # clear old sockets
            if hasattr(self, 'inputs') and hasattr(self, 'outputs'):
                # remove grSockets from scene
                for socket in (self.inputs+self.outputs):
                    self.scene.grScene.removeItem(socket.grSocket)
                self.inputs = []
                self.outputs = []

        # create new sockets
        counter = 0
        for idx, item in enumerate(inputs):
            data_type = getattr(item, 'data_type', 'null')
            if isinstance(data_type, str):
                try:
                    data_type = DataType(data_type)
                except Exception:
                    pass
            socket = self.Socket_class(
                self,
                index=idx,
                position=LEFT_CENTER,
                socket_name=getattr(item, 'port_name', ''),
                socket_type=data_type,
                multi_edges=True,
                count_on_this_node_side=len(inputs),
                is_input=True,
                label=getattr(item, 'label', None)
            )
            counter += 1
            self.inputs.append(socket)

        counter = 0
        for idx, item in enumerate(outputs):
            data_type = getattr(item, 'data_type', 'null')
            if isinstance(data_type, str):
                try:
                    data_type = DataType(data_type)
                except Exception:
                    pass
            socket = self.Socket_class(
                self,
                index=idx,
                position=RIGHT_CENTER,
                socket_name=getattr(item, 'port_name', ''),
                socket_type=data_type,
                multi_edges=True,
                count_on_this_node_side=len(outputs),
                is_input=False,
                label=getattr(item, 'label', None)
            )
            counter += 1
            self.outputs.append(socket)


    def onEdgeConnectionChanged(self, new_edge: 'Edge'):
        """
        Event handling that any connection (`Edge`) has changed. Currently not used...

        :param new_edge: reference to the changed :class:`~nodeeditor.node_edge.Edge`
        :type new_edge: :class:`~nodeeditor.node_edge.Edge`
        """
        pass

    def onInputChanged(self, socket: 'Socket'):
        """Event handling when Node's input Edge has changed. We auto-mark this `Node` as dirty.

        :param socket: reference to the changed :class:`~nodeeditor.node_socket.Socket`
        :type socket: :class:`~nodeeditor.node_socket.Socket`
        """
        self.markDirty()
        self.markDescendantsDirty()

    def onDeserialized(self, data: dict):
        """Event manually called when this node was deserialized. Currently called when node is deserialized from scene
        clipboard. We restore here proper selection state for the node.

        :param data: dictionary containing parsed node serialized data
        :type data: ``dict``
        """
        pass

    def onDoubleClicked(self, event=None):
        """Event handling double click on Graphics Node in `Scene`"""
        pass

    def doSelect(self, new_state: bool=True):
        """Safe version of selecting the `Node`. Takes care about the selection state flag used internally

        :param new_state: ``True`` if you want to select the `Node`. ``False`` if you want to deselect the `Node`
        :type new_state: ``bool``
        """
        self.grNode.doSelect(new_state)

    def isSelected(self):
        """Returns ``True`` if current `Node` is selected"""
        return self.grNode.isSelected()

    def hasConnectedEdge(self, edge_type=None):
        """
        Returns ``True`` if node has at least one edge connected. If parameter is provided, it will check
        if node has at least one edge of that type connected.

        :param edge_type: Check for a specific edge type
        :type edge_type: ``int``
        :return: ``True`` if any or specific `Edge` is connected to this `Node`
        :rtype: ``bool``
        """
        for socket in (self.inputs + self.outputs):
            for edge in socket.edges:
                if edge_type is None or edge.edge_type == edge_type:
                    return True
        return False

    def getSocketPosition(self, index: int, position: int, num_out_of: int=1) -> List[float]:
        """
        Get the relative `Socket` position for a given `Socket`

        :param index: Order number of the Socket. (0, 1, 2, ...)
        :type index: ``int``
        :param position: `Socket Position Constant` describing where the Socket is located
        :type position: ``int``
        :param num_out_of: Total number of Sockets on this `Socket Position`
        :type num_out_of: ``int``
        :return: Position of described Socket on the `Node`
        :rtype: ``list`` [x, y]
        """
        x = self.socket_offsets[position] if (position in self.socket_offsets.keys()) else 0

        if position in (LEFT_TOP, LEFT_CENTER, LEFT_BOTTOM):
            # start from left - offset'i direkt kullan
            x = x

        else:
            # start from right - offset'i direkt kullan
            x = self.grNode.width + x

        if position in (LEFT_BOTTOM, RIGHT_BOTTOM):
            # start from bottom
            y = self.grNode.height - self.grNode.edge_roundness - self.grNode.title_vertical_padding - index * self.socket_spacing
        elif position in (LEFT_CENTER, RIGHT_CENTER):
            num_sockets = num_out_of
            node_height = self.grNode.height
            top_offset = self.grNode.title_height + 2 * self.grNode.title_vertical_padding + self.grNode.edge_roundness
            available_height = node_height - top_offset

            total_height_of_all_sockets = num_sockets * self.socket_spacing
            new_top = available_height - total_height_of_all_sockets

            # y = top_offset + index * self.socket_spacing + new_top / 2
            y = top_offset + available_height / 2.0 + (index - 0.5 * (num_sockets - 1)) * self.socket_spacing
            if num_sockets > 1:
                y -= self.socket_spacing / 2

        elif position in (LEFT_TOP, RIGHT_TOP):
            # start from top
            y = self.grNode.title_height + self.grNode.title_vertical_padding + self.grNode.edge_roundness + index * self.socket_spacing
        else:
            # this should never happen
            y = 0

        return [x, y]

    def getSocketScenePosition(self, socket: 'Socket') -> Tuple[float, float]:
        """
        Get absolute Socket position in the Scene

        :param socket: `Socket` which position we want to know
        :return: (x, y) Socket's scene position
        """
        nodepos = self.grNode.pos()
        socketpos = self.getSocketPosition(socket.index, socket.position, socket.count_on_this_node_side)
        return (nodepos.x() + socketpos[0], nodepos.y() + socketpos[1])

    def updateConnectedEdges(self):
        """Recalculate (Refresh) positions of all connected `Edges`. Used for updating Graphics Edges"""
        for socket in self.inputs + self.outputs:
            # if socket.hasEdge():
            for edge in socket.edges:
                edge.updatePositions()

    def remove(self):
        """
        Safely remove this Node
        """
        if DEBUG: print("> Removing Node", self)
        if DEBUG: print(" - remove all edges from sockets")
        for socket in (self.inputs+self.outputs):
            # if socket.hasEdge():
            for edge in socket.edges.copy():
                if DEBUG: print("    - removing from socket:", socket, "edge:", edge)
                edge.remove()
                
        # Cleanup thumbnailer if exists
        if hasattr(self, 'grNode') and self.grNode and hasattr(self.grNode, 'thumb_layer') and self.grNode.thumb_layer:
            if DEBUG: print(" - cleanup thumbnailer")
            self.grNode.thumb_layer.cleanup()
            self.grNode.thumb_layer = None
            
        if DEBUG: print(" - remove grNode")
        self.scene.grScene.removeItem(self.grNode)
        self.grNode = None
        if DEBUG: print(" - remove node from the scene")
        self.scene.removeNode(self)
        if DEBUG: print(" - everything was done.")


    # node evaluation stuff

    def isDirty(self) -> bool:
        """Is this node marked as `Dirty`

        :return: ``True`` if `Node` is marked as `Dirty`
        :rtype: ``bool``
        """
        return self._is_dirty

    def markDirty(self, new_value: bool=True):
        """Mark this `Node` as `Dirty`. See :ref:`evaluation` for more

        :param new_value: ``True`` if this `Node` should be `Dirty`. ``False`` if you want to un-dirty this `Node`
        :type new_value: ``bool``
        """
        self._is_dirty = new_value
        if self._is_dirty: self.onMarkedDirty()

    def onMarkedDirty(self):
        """Called when this `Node` has been marked as `Dirty`. This method is supposed to be overridden"""
        pass

    def markChildrenDirty(self, new_value: bool=True):
        """Mark all first level children of this `Node` to be `Dirty`. Not this `Node` it self. Not other descendants

        :param new_value: ``True`` if children should be `Dirty`. ``False`` if you want to un-dirty children
        :type new_value: ``bool``
        """
        for other_node in self.getChildrenNodes():
            other_node.markDirty(new_value)

    def markDescendantsDirty(self, new_value: bool=True):
        """Mark all children and descendants of this `Node` to be `Dirty`. Not this `Node` it self

        :param new_value: ``True`` if children and descendants should be `Dirty`. ``False`` if you want to un-dirty children and descendants
        :type new_value: ``bool``
        """
        for other_node in self.getChildrenNodes():
            other_node.markDirty(new_value)
            other_node.markDescendantsDirty(new_value)

    def isInvalid(self) -> bool:
        """Is this node marked as `Invalid`?

        :return: ``True`` if `Node` is marked as `Invalid`
        :rtype: ``bool``
        """
        return self._is_invalid

    def markInvalid(self, new_value: bool=True):
        """Mark this `Node` as `Invalid`. See :ref:`evaluation` for more

        :param new_value: ``True`` if this `Node` should be `Invalid`. ``False`` if you want to un-invalid this `Node`
        :type new_value: ``bool``
        """
        self._is_invalid = new_value
        if self._is_invalid: self.onMarkedInvalid()

    def onMarkedInvalid(self):
        """Called when this `Node` has been marked as `Invalid`. This method is supposed to be overridden"""
        pass

    def markChildrenInvalid(self, new_value: bool=True):
        """Mark all first level children of this `Node` to be `Invalid`. Not this `Node` it self. Not other descendants

        :param new_value: ``True`` if children should be `Invalid`. ``False`` if you want to un-invalid children
        :type new_value: ``bool``
        """
        for other_node in self.getChildrenNodes():
            other_node.markInvalid(new_value)

    def markDescendantsInvalid(self, new_value: bool=True):
        """Mark all children and descendants of this `Node` to be `Invalid`. Not this `Node` it self

        :param new_value: ``True`` if children and descendants should be `Invalid`. ``False`` if you want to un-invalid children and descendants
        :type new_value: ``bool``
        """
        for other_node in self.getChildrenNodes():
            other_node.markInvalid(new_value)
            other_node.markDescendantsInvalid(new_value)

    def eval(self, index=0):
        """Evaluate this `Node`. This is supposed to be overridden. See :ref:`evaluation` for more"""
        self.markDirty(False)
        self.markInvalid(False)
        return 0

    def evalChildren(self):
        """Evaluate all children of this `Node`"""
        for node in self.getChildrenNodes():
            node.eval()

    # traversing nodes functions

    def getChildrenNodes(self) -> 'List[Node]':
        """
        Collect all children nodes of this `Node` and return them as list of `Nodes`.
        Children nodes are all nodes connected to Output sockets of this node.

        :return: list of children :class:`~nodeeditor.node_node.Node`
        :rtype: List[:class:`~nodeeditor.node_node.Node`]
        """
        if not self.outputs: return []
        other_nodes = []
        for ix in range(len(self.outputs)):
            for edge in self.outputs[ix].edges:
                other_node = edge.getOtherSocket(self.outputs[ix]).node
                other_nodes.append(other_node)
        return other_nodes


    def getInputsNodes(self) -> 'List[Node]':
        """
        Collect all input nodes of this `Node` and return them as list of `Nodes`.
        Input nodes are all nodes connected to Input sockets of this node.

        :return: list of input :class:`~nodeeditor.node_node.Node`
        :rtype: List[:class:`~nodeeditor.node_node.Node`]
        """
        if not self.inputs: return []
        other_nodes = []
        for ix in range(len(self.inputs)):
            for edge in self.inputs[ix].edges:
                other_node = edge.getOtherSocket(self.inputs[ix]).node
                other_nodes.append(other_node)
        return other_nodes

    # serialization functions

    def serialize(self) -> OrderedDict:
        inputs, outputs = [], []
        for socket in self.inputs: inputs.append(socket.serialize())
        for socket in self.outputs: outputs.append(socket.serialize())
        ser_dict = OrderedDict([
            ('id', self.id),
            ('title', self.title),
            ('pos_x', self.grNode.scenePos().x()),
            ('pos_y', self.grNode.scenePos().y()),
            ('inputs', inputs),
            ('outputs', outputs),
            ('content', self.content.serialize() if self.content else {}),
            ('node_type', self.node_type),
            ('properties', self.property_manager.serialize() if hasattr(self, 'property_manager') else {}),
        ])
        return ser_dict

    def deserialize(self, data: dict, hashmap: dict={}, restore_id: bool=True, *args, **kwargs) -> bool:
        try:
            if restore_id: self.id = data['id']
            hashmap[data['id']] = self

            self.setPos(data['pos_x'], data['pos_y'])
            self.title = data['title']

            data['inputs'].sort(key=lambda socket: socket['index'] if 'index' in socket else 0)
            data['outputs'].sort(key=lambda socket: socket['index'] if 'index' in socket else 0)

            for socket_data in data['inputs']:
                found = None
                for socket in self.inputs:
                    if socket.index == socket_data['index']:
                        found = socket
                        break
                if found is None:
                    # create new socket
                    self.inputs.append(Socket(
                        node=self, index=socket_data['index'], position=socket_data['position'],
                        socket_type=socket_data['socket_type'], count_on_this_node_side=len(data['inputs']),
                        is_input=True
                    ))
                else:
                    found.deserialize(socket_data, hashmap, restore_id)
            
            for socket_data in data['outputs']:
                found = None
                for socket in self.outputs:
                    if socket.index == socket_data['index']:
                        found = socket
                        break
                if found is None:
                    # create new socket
                    self.outputs.append(Socket(
                        node=self, index=socket_data['index'], position=socket_data['position'],
                        socket_type=socket_data['socket_type'], count_on_this_node_side=len(data['outputs']),
                        is_input=False
                    ))
                else:
                    found.deserialize(socket_data, hashmap, restore_id)

        except Exception as e: dumpException(e)

        # also deserialize the content of the node
        if self.content is not None and 'content' in data:
            self.content.deserialize(data['content'], hashmap)

        # deserialize properties
        if hasattr(self, 'property_manager') and 'properties' in data:
            self.property_manager.deserialize(data['properties'], hashmap, restore_id)

        return True
